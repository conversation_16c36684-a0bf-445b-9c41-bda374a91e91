name: CI Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        nextflow_version: ['21.10.3', '22.10.1', '23.04.0']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Set up R
      uses: r-lib/actions/setup-r@v2
      with:
        r-version: '4.2.1'
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pandas numpy biopython pysam pyvcf matplotlib seaborn
    
    - name: Install R dependencies
      run: |
        R -e "install.packages(c('dplyr', 'ggplot2', 'readr', 'tidyr', 'stringr', 'RColorBrewer'), repos='https://cran.rstudio.com/')"
    
    - name: Install Nextflow
      run: |
        wget -qO- https://get.nextflow.io | bash
        sudo mv nextflow /usr/local/bin/
        nextflow -version
    
    - name: Validate pipeline setup
      run: |
        python3 bin/validate_setup.py
    
    - name: Test samplesheet validation
      run: |
        python3 bin/metadata_validator.py --input assets/samplesheet_test.csv
    
    - name: Test Nextflow syntax
      run: |
        nextflow run main.nf --help
    
    - name: Run pipeline dry run
      run: |
        nextflow run main.nf -profile test --outdir test_results --dry-run
    
    - name: Test with small dataset (if Docker available)
      run: |
        if command -v docker &> /dev/null; then
          echo "Docker available, running full test"
          # nextflow run main.nf -profile test,docker --outdir test_results
          echo "Full test would run here"
        else
          echo "Docker not available, skipping full test"
        fi

  lint:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black
    
    - name: Lint Python code
      run: |
        flake8 bin/*.py --max-line-length=100 --ignore=E203,W503
        black --check bin/*.py
    
    - name: Check file permissions
      run: |
        # Check that scripts are executable
        test -x bin/track_clones.R
        test -x bin/merge_variants.py
        test -x bin/metadata_validator.py
        test -x bin/validate_setup.py
        test -x bin/test_pipeline.sh
