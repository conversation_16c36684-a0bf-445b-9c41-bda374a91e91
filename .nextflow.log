Jul-09 22:33:58.769 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker --run_tcr false
Jul-09 22:33:58.797 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 22:33:58.810 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 22:33:58.827 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 22:33:58.828 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 22:33:58.829 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 22:33:58.834 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 22:33:58.842 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:33:58.843 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:33:58.856 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 22:33:58.860 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@62ddd21b] - activable => nextflow.secret.LocalSecretsProvider@62ddd21b
Jul-09 22:33:58.866 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 22:33:59.277 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 22:33:59.291 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 22:33:59.298 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [soggy_shannon] DSL2 - revision: 149844d342
Jul-09 22:33:59.299 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 22:33:59.299 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 22:33:59.321 [main] DEBUG nextflow.Session - Session UUID: 5be12f49-bbfd-41ff-b458-4700e420002d
Jul-09 22:33:59.321 [main] DEBUG nextflow.Session - Run name: soggy_shannon
Jul-09 22:33:59.321 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 22:33:59.325 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 22:33:59.326 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:33:59.342 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (68.1 MB) - Swap: 5 GB (1.4 GB)
Jul-09 22:33:59.348 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 22:33:59.362 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 22:33:59.365 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 22:33:59.372 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 22:33:59.381 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 22:33:59.385 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 22:33:59.411 [main] DEBUG nextflow.Session - Session start
Jul-09 22:33:59.412 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_22-33-59.txt
Jul-09 22:33:59.415 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:33:59.423 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:33:59.571 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 22:34:00.141 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 22:34:00.202 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:34:00.203 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:34:00.207 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.208 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.210 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 22:34:00.213 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 22:34:00.214 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 22:34:00.222 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:34:00.254 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:34:00.254 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:34:00.255 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.255 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.255 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 22:34:00.266 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:34:00.267 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FILTERMUTECTCALLS` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:34:00.268 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.268 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.268 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 22:34:00.275 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:34:00.277 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.277 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.277 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 22:34:00.282 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:34:00.283 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:34:00.284 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.284 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.284 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:34:00.290 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:34:00.290 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:34:00.291 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.291 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.291 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:34:00.295 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:34:00.296 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:34:00.297 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.297 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.297 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 22:34:00.303 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:34:00.304 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.304 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.305 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 22:34:00.312 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:34:00.313 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.313 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.313 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 22:34:00.316 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:34:00.316 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:34:00.317 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.317 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.317 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 22:34:00.321 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:34:00.322 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.322 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.322 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:34:00.326 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:34:00.327 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:34:00.327 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:34:00.328 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:34:00.332 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, TRACK_CLONES, PARSE_HLA_TYPES, OPTITYPE, MUTECT2, MERGE_VARIANTS, NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:34:00.338 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 22:34:00.338 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 22:34:00.339 [main] DEBUG nextflow.Session - Igniting dataflow network (29)
Jul-09 22:34:00.342 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:34:00.342 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:34:00.342 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:34:00.342 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:34:00.343 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:34:00.343 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_e25af700512e8009: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_796f98b8be950eb4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_d59b4ec83f7f552d: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_08366ba8b5bdcd21: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_50fff6088c7a6121: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
  Script_5e083e27b34891ea: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-09 22:34:00.343 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 22:34:00.343 [main] DEBUG nextflow.Session - Session await
Jul-09 22:34:00.425 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:00.426 [Task submitter] INFO  nextflow.Session - [f2/737c4f] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:34:00.431 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:00.431 [Task submitter] INFO  nextflow.Session - [4e/a0b734] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N)
Jul-09 22:34:00.434 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:00.434 [Task submitter] INFO  nextflow.Session - [03/9ac5e0] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:34:00.437 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:00.437 [Task submitter] INFO  nextflow.Session - [9e/571e4f] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:34:00.530 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:00.530 [Task submitter] INFO  nextflow.Session - [04/23adf1] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:34:00.535 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:00.535 [Task submitter] INFO  nextflow.Session - [ba/ff401b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 22:34:00.766 [Actor Thread 5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:34:01.033 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:34:01.118 [Actor Thread 14] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:34:01.434 [Actor Thread 5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:34:01.759 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:34:01.993 [Actor Thread 14] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:34:02.037 [FileTransfer-1] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-5be12f49-bbfd-41ff-b458-4700e420002d/43/166d1b1bda7a95a87018203790c8a8/genome.fasta
Jul-09 22:34:02.141 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:34:02.178 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:34:02.212 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:34:02.350 [Actor Thread 14] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:34:02.365 [FileTransfer-2] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-5be12f49-bbfd-41ff-b458-4700e420002d/8d/f5b66a0a878bd15f6f16720c079e84/genome.gtf
Jul-09 22:34:02.366 [FileTransfer-3] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-5be12f49-bbfd-41ff-b458-4700e420002d/22/dc16412bae84b2a3703fbe1dbcfe95/genome.fasta.fai
Jul-09 22:34:02.366 [FileTransfer-4] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.dict to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-5be12f49-bbfd-41ff-b458-4700e420002d/91/d4ebeffa3b584605ded6bc977a7b22/genome.dict
Jul-09 22:34:02.483 [FileTransfer-3] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:34:02.483 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:34:02.556 [FileTransfer-4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:34:02.587 [FileTransfer-3] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:34:02.616 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:34:02.652 [FileTransfer-4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:34:02.664 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:02.665 [Task submitter] INFO  nextflow.Session - [79/ea466b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta)
Jul-09 22:34:02.696 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:02.697 [Task submitter] INFO  nextflow.Session - [0a/030063] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01)
Jul-09 22:34:04.389 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/79/ea466bce291aa444d186210ccad9e9]
Jul-09 22:34:04.390 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:34:04.397 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:04.398 [Task submitter] INFO  nextflow.Session - [04/9e1b73] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01)
Jul-09 22:34:04.408 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:34:08.879 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0a/0300636beac78527ac7ddfdc041e22]
Jul-09 22:34:08.883 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:34:08.883 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0a/0300636beac78527ac7ddfdc041e22
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01)` terminated with an error exit status (2)
Jul-09 22:34:08.883 [Task submitter] INFO  nextflow.Session - [34/341546] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)
Jul-09 22:34:08.894 [TaskFinalizer-2] ERROR nextflow.processor.TaskProcessor - Error executing process > 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01)'

Caused by:
  Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01)` terminated with an error exit status (2)


Command executed:

  gatk Mutect2 \
      --input SAMPLE_01_cfDNA_wes_1.fastq.gz --input SAMPLE_01_cfDNA_wes_2.fastq.gz \
      --input SAMPLE_01_N_wes_1.fastq.gz --input SAMPLE_01_N_wes_2.fastq.gz \
      --reference genome.fasta \
      --output PATIENT_01.vcf.gz \
      --tumor-sample PATIENT_01_tumor \
      --normal-sample PATIENT_01_normal \
  
  
  cat <<-END_VERSIONS > versions.yml
  "IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2":
      gatk4: $(echo $(gatk --version 2>&1) | sed 's/^.*(GATK) v//; s/ .*$//')
  END_VERSIONS

Command exit status:
  2

Command output:
  (empty)

Command error:
  WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
  05:34:07.895 INFO  NativeLibraryLoader - Loading libgkl_compression.so from jar:file:/gatk/gatk-package-*******-local.jar!/com/intel/gkl/native/libgkl_compression.so
  05:34:08.323 INFO  Mutect2 - ------------------------------------------------------------
  05:34:08.326 INFO  Mutect2 - The Genome Analysis Toolkit (GATK) v*******
  05:34:08.327 INFO  Mutect2 - For support and documentation go to https://software.broadinstitute.org/gatk/
  05:34:08.327 INFO  Mutect2 - Executing as root@e89e4d1616b9 on Linux v6.10.14-linuxkit amd64
  05:34:08.327 INFO  Mutect2 - Java runtime: OpenJDK 64-Bit Server VM v1.8.0_242-8u242-b08-0ubuntu3~18.04-b08
  05:34:08.327 INFO  Mutect2 - Start Date/Time: July 10, 2025 5:34:07 AM GMT
  05:34:08.328 INFO  Mutect2 - ------------------------------------------------------------
  05:34:08.328 INFO  Mutect2 - ------------------------------------------------------------
  05:34:08.329 INFO  Mutect2 - HTSJDK Version: 3.0.1
  05:34:08.329 INFO  Mutect2 - Picard Version: 2.27.5
  05:34:08.329 INFO  Mutect2 - Built for Spark Version: 2.4.5
  05:34:08.329 INFO  Mutect2 - HTSJDK Defaults.COMPRESSION_LEVEL : 2
  05:34:08.329 INFO  Mutect2 - HTSJDK Defaults.USE_ASYNC_IO_READ_FOR_SAMTOOLS : false
  05:34:08.329 INFO  Mutect2 - HTSJDK Defaults.USE_ASYNC_IO_WRITE_FOR_SAMTOOLS : true
  05:34:08.329 INFO  Mutect2 - HTSJDK Defaults.USE_ASYNC_IO_WRITE_FOR_TRIBBLE : false
  05:34:08.329 INFO  Mutect2 - Deflater: IntelDeflater
  05:34:08.329 INFO  Mutect2 - Inflater: IntelInflater
  05:34:08.329 INFO  Mutect2 - GCS max retries/reopens: 20
  05:34:08.329 INFO  Mutect2 - Requester pays: disabled
  05:34:08.329 INFO  Mutect2 - Initializing engine
  05:34:08.675 INFO  Mutect2 - Shutting down engine
  [July 10, 2025 5:34:08 AM GMT] org.broadinstitute.hellbender.tools.walkers.mutect.Mutect2 done. Elapsed time: 0.01 minutes.
  Runtime.totalMemory()=381681664
  ***********************************************************************
  
  A USER ERROR has occurred: Input files reference and reads have incompatible contigs: No overlapping contigs found.
    reference contigs = [chr22]
    reads contigs = []
  
  ***********************************************************************
  Set the system property GATK_STACKTRACE_ON_USER_EXCEPTION (--java-options '-DGATK_STACKTRACE_ON_USER_EXCEPTION=true') to print the stack trace.
  Using GATK jar /gatk/gatk-package-*******-local.jar
  Running:
      java -Dsamjdk.use_async_io_read_samtools=false -Dsamjdk.use_async_io_write_samtools=true -Dsamjdk.use_async_io_write_tribble=false -Dsamjdk.compression_level=2 -jar /gatk/gatk-package-*******-local.jar Mutect2 --input SAMPLE_01_cfDNA_wes_1.fastq.gz --input SAMPLE_01_cfDNA_wes_2.fastq.gz --input SAMPLE_01_N_wes_1.fastq.gz --input SAMPLE_01_N_wes_2.fastq.gz --reference genome.fasta --output PATIENT_01.vcf.gz --tumor-sample PATIENT_01_tumor --normal-sample PATIENT_01_normal

Work dir:
  /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0a/0300636beac78527ac7ddfdc041e22

Container:
  broadinstitute/gatk:*******

Tip: you can replicate the issue by changing to the process work dir and entering the command `bash .command.run`
Jul-09 22:34:08.917 [TaskFinalizer-2] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-09 22:34:08.920 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 22:34:08.973 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/03/9ac5e095c3488c3ad79e528f6b1958]
Jul-09 22:34:09.014 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/04/23adf1122a2035934afed1c7444296]
Jul-09 22:34:09.385 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/04/9e1b731b8a2770aae9630039e1d763]
Jul-09 22:34:09.386 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/04/9e1b731b8a2770aae9630039e1d763
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (PATIENT_01)` terminated with an error exit status (2)
Jul-09 22:34:10.418 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/34/34154692784fa38a0e24adee8e39fa]
Jul-09 22:34:10.420 [TaskFinalizer-6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/34/34154692784fa38a0e24adee8e39fa
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)` terminated with an error exit status (1)
Jul-09 22:34:12.681 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ba/ff401bc2d8d99ce74034880b3e654d]
Jul-09 22:34:12.705 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/4e/a0b7340169d942da51c03755a7fede]
Jul-09 22:34:12.744 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/9e/571e4f80c3c339c85185dafba911a4]
Jul-09 22:34:13.564 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/f2/737c4f5179f04aa6716a5a9934d45a]
Jul-09 22:34:13.565 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 22:34:13.565 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 22:34:13.568 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-09 22:34:13.568 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-09 22:34:13.572 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=7; failedCount=3; ignoredCount=0; cachedCount=0; pendingCount=1; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=2m; failedDuration=24.9s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=8; peakCpus=16; peakMemory=48 GB; ]
Jul-09 22:34:13.572 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 22:34:13.572 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 22:34:14.055 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 22:34:14.131 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 22:34:14.297 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 22:34:14.309 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-09 22:34:14.310 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
