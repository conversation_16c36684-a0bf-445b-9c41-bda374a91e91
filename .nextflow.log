Jul-09 22:17:30.896 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 22:17:30.924 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 22:17:30.937 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 22:17:30.954 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 22:17:30.954 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 22:17:30.955 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 22:17:30.961 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 22:17:30.968 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:17:30.969 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:17:30.987 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 22:17:30.988 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@21ab988f] - activable => nextflow.secret.LocalSecretsProvider@21ab988f
Jul-09 22:17:30.991 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 22:17:31.395 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 22:17:31.408 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 22:17:31.415 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [loquacious_newton] DSL2 - revision: 149844d342
Jul-09 22:17:31.416 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 22:17:31.416 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 22:17:31.439 [main] DEBUG nextflow.Session - Session UUID: 303708ab-8788-4078-ae1d-ba3af59b5d49
Jul-09 22:17:31.439 [main] DEBUG nextflow.Session - Run name: loquacious_newton
Jul-09 22:17:31.439 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 22:17:31.443 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 22:17:31.445 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:17:31.463 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (70.3 MB) - Swap: 5 GB (1.2 GB)
Jul-09 22:17:31.470 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 22:17:31.484 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 22:17:31.487 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 22:17:31.495 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 22:17:31.504 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 22:17:31.509 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 22:17:31.533 [main] DEBUG nextflow.Session - Session start
Jul-09 22:17:31.535 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_22-17-31.txt
Jul-09 22:17:31.537 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:17:31.539 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:17:31.702 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 22:17:32.243 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 22:17:32.302 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:17:32.304 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:17:32.308 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.308 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.310 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 22:17:32.313 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 22:17:32.314 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 22:17:32.321 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:17:32.358 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:17:32.358 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:17:32.360 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.360 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.360 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 22:17:32.367 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:17:32.368 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FILTERMUTECTCALLS` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:17:32.369 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.369 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.369 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 22:17:32.374 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 22:17:32.374 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:OPTITYPE` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 22:17:32.375 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.375 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.376 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE': maxForks=0; fair=false; array=0
Jul-09 22:17:32.382 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:17:32.383 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.383 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.383 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 22:17:32.388 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:17:32.389 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:17:32.389 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.389 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.390 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:17:32.394 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:17:32.395 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:17:32.395 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.395 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.396 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:17:32.400 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:17:32.400 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:17:32.401 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.401 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.401 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 22:17:32.407 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:17:32.408 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.408 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.408 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 22:17:32.413 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 22:17:32.414 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 22:17:32.415 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.415 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.415 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:17:32.422 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 22:17:32.422 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_ANALYZE` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 22:17:32.423 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.423 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.423 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE': maxForks=0; fair=false; array=0
Jul-09 22:17:32.427 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 22:17:32.427 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 22:17:32.428 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.428 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.428 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-09 22:17:32.433 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 22:17:32.433 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:TRACK_CLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 22:17:32.434 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.434 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.435 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES': maxForks=0; fair=false; array=0
Jul-09 22:17:32.444 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:17:32.445 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.445 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.445 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 22:17:32.449 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:17:32.450 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:17:32.451 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.451 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.451 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 22:17:32.455 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:17:32.456 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.456 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.456 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:17:32.462 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:17:32.463 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:17:32.463 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:17:32.463 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:17:32.468 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, TRACK_CLONES, PARSE_HLA_TYPES, OPTITYPE, MUTECT2, MERGE_VARIANTS, NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE, SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:17:32.475 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 22:17:32.475 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 22:17:32.477 [main] DEBUG nextflow.Session - Igniting dataflow network (34)
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 22:17:32.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 22:17:32.480 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 22:17:32.480 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:17:32.480 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:17:32.480 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:17:32.480 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:17:32.481 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_796f98b8be950eb4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
  Script_e25af700512e8009: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_e31ace2ae3a0577f: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_08366ba8b5bdcd21: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_20ae2808df92087c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
  Script_5e083e27b34891ea: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-09 22:17:32.481 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 22:17:32.481 [main] DEBUG nextflow.Session - Session await
Jul-09 22:17:32.575 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.576 [Task submitter] INFO  nextflow.Session - [c0/7753f6] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 22:17:32.581 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.581 [Task submitter] INFO  nextflow.Session - [8d/059ecf] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:17:32.584 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.584 [Task submitter] INFO  nextflow.Session - [1a/2736cc] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T)
Jul-09 22:17:32.587 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.588 [Task submitter] INFO  nextflow.Session - [c0/301fe6] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 22:17:32.591 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.591 [Task submitter] INFO  nextflow.Session - [04/e4f3b0] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:17:32.594 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.594 [Task submitter] INFO  nextflow.Session - [d1/913d4b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA)
Jul-09 22:17:32.597 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.597 [Task submitter] INFO  nextflow.Session - [79/4694bb] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)
Jul-09 22:17:32.600 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:32.600 [Task submitter] INFO  nextflow.Session - [fd/cd9e7b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:17:32.947 [Actor Thread 16] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:17:33.183 [Actor Thread 28] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:17:33.282 [Actor Thread 20] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:17:33.495 [Actor Thread 20] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:17:33.883 [Actor Thread 16] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:17:34.175 [Actor Thread 28] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:17:34.255 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/1a/2736cca712118940bcde77fd1e07e0]
Jul-09 22:17:34.256 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:17:34.259 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:34.259 [Task submitter] INFO  nextflow.Session - [8a/cce8a2] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:17:34.261 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/1a/2736cca712118940bcde77fd1e07e0
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T)` terminated with an error exit status (2)
Jul-09 22:17:34.267 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/d1/913d4b1836bc2418dc5d69df642943]
Jul-09 22:17:34.270 [TaskFinalizer-1] ERROR nextflow.processor.TaskProcessor - Error executing process > 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T)'

Caused by:
  Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T)` terminated with an error exit status (2)


Command executed:

  OptiTypePipeline.py \
      --input SAMPLE_01_T_wes_1.fastq.gz SAMPLE_01_T_wes_2.fastq.gz \
      --dna \
      --prefix SAMPLE_01_T \
      --outdir . \
  
  
  cat <<-END_VERSIONS > versions.yml
  "IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE":
      optitype: $(OptiTypePipeline.py --version 2>&1 | grep -o 'OptiType [0-9.]*' | cut -d' ' -f2)
  END_VERSIONS

Command exit status:
  2

Command output:
  (empty)

Command error:
  WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
  usage: OptiType [-h] --input FQ [FQ ...] (--rna | --dna) [--beta B]
                  [--enumerate N] --outdir OUTDIR [--prefix PREFIX] [--verbose]
                  [--config CONFIG]
  OptiType: error: argument --config/-c: can't open 'eval export PYTHONNOUSERSITE="1"
  export R_PROFILE_USER="/.Rprofile"
  export R_ENVIRON_USER="/.Renviron"
  export JULIA_DEPOT_PATH="/usr/local/share/julia"
  export PATH="$PATH:/Users/<USER>/Downloads/immune_neoantigen_pipeline/bin"; /bin/bash -euo pipefail .command.run nxf_trace': [Errno 2] No such file or directory: 'eval export PYTHONNOUSERSITE="1"\nexport R_PROFILE_USER="/.Rprofile"\nexport R_ENVIRON_USER="/.Renviron"\nexport JULIA_DEPOT_PATH="/usr/local/share/julia"\nexport PATH="$PATH:/Users/<USER>/Downloads/immune_neoantigen_pipeline/bin"; /bin/bash -euo pipefail .command.run nxf_trace'

Work dir:
  /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/1a/2736cca712118940bcde77fd1e07e0

Container:
  fred2/optitype:latest

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
Jul-09 22:17:34.270 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:34.284 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/79/4694bb49f8d9292f2aeaa4c900f330]
Jul-09 22:17:34.289 [Task submitter] INFO  nextflow.Session - [12/3ebf0a] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T)
Jul-09 22:17:34.290 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/d1/913d4b1836bc2418dc5d69df642943
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA)` terminated with an error exit status (2)
Jul-09 22:17:34.291 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/79/4694bb49f8d9292f2aeaa4c900f330
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)` terminated with an error exit status (2)
Jul-09 22:17:34.293 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:17:34.294 [Task submitter] INFO  nextflow.Session - [31/68a293] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:17:34.297 [TaskFinalizer-1] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-09 22:17:34.300 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 22:17:34.302 [Actor Thread 6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 22:17:34.303 [Actor Thread 18] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 22:17:34.303 [Actor Thread 10] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 22:17:34.368 [Actor Thread 20] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:17:34.520 [FileTransfer-1] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-303708ab-8788-4078-ae1d-ba3af59b5d49/61/b1ec3e53cec788559a3814e10b3bdc/genome.fasta
Jul-09 22:17:34.520 [FileTransfer-2] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-303708ab-8788-4078-ae1d-ba3af59b5d49/75/f7b9404f8fc469327a53abe0e1cfd9/genome.gtf
Jul-09 22:17:34.551 [Actor Thread 28] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:17:34.623 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:17:34.631 [FileTransfer-4] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.dict to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-303708ab-8788-4078-ae1d-ba3af59b5d49/c1/668981bf119a6122b0be16e91f78f7/genome.dict
Jul-09 22:17:34.631 [FileTransfer-3] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-303708ab-8788-4078-ae1d-ba3af59b5d49/87/27b1c774a7133f425b55a20c24f86e/genome.fasta.fai
Jul-09 22:17:34.636 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:17:34.664 [FileTransfer-4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:17:34.665 [FileTransfer-3] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:17:34.709 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:17:34.719 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:17:34.744 [FileTransfer-3] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 22:17:34.751 [FileTransfer-4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 22:17:35.733 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/12/3ebf0a7fbabfc15dfc5569b0efe41c]
Jul-09 22:17:35.734 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/12/3ebf0a7fbabfc15dfc5569b0efe41c
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T)` terminated with an error exit status (2)
Jul-09 22:17:41.391 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/fd/cd9e7be11681eb672e4491d43438f5]
Jul-09 22:17:41.409 [TaskFinalizer-5] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:17:41.507 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/8d/059ecf4785283d7328ca00141fdf70]
Jul-09 22:17:45.368 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/04/e4f3b0ee250c8f1648874fdf4bd5af]
Jul-09 22:17:46.316 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/c0/7753f644d66e17dc9851c749d6a7e6]
Jul-09 22:17:46.337 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/c0/301fe6bab2927ce633e785810ebadf]
Jul-09 22:17:46.669 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/8a/cce8a2c1058636ae5159d08104be2d]
Jul-09 22:17:46.773 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/31/68a293842b0f0923696e7fee0b9e36]
Jul-09 22:17:46.773 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 22:17:46.773 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 22:17:46.776 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-09 22:17:46.776 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-09 22:17:46.780 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=7; failedCount=4; ignoredCount=0; cachedCount=0; pendingCount=6; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=2m 22s; failedDuration=12.7s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=9; peakCpus=18; peakMemory=54 GB; ]
Jul-09 22:17:46.780 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 22:17:46.780 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 22:17:47.259 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 22:17:47.340 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 22:17:47.426 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 22:17:47.438 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-09 22:17:47.438 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
