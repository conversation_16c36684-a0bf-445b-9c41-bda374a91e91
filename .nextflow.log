Jul-09 22:54:50.828 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 22:54:50.857 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 22:54:50.870 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 22:54:50.889 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 22:54:50.889 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 22:54:50.890 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 22:54:50.896 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 22:54:50.904 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:54:50.906 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:54:50.924 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 22:54:50.925 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@21ab988f] - activable => nextflow.secret.LocalSecretsProvider@21ab988f
Jul-09 22:54:50.927 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 22:54:51.356 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 22:54:51.370 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 22:54:51.377 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [elated_wegener] DSL2 - revision: 149844d342
Jul-09 22:54:51.378 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 22:54:51.378 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 22:54:51.402 [main] DEBUG nextflow.Session - Session UUID: 1ac36788-5c90-4371-a853-9649e4d437ff
Jul-09 22:54:51.403 [main] DEBUG nextflow.Session - Run name: elated_wegener
Jul-09 22:54:51.403 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 22:54:51.406 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 22:54:51.408 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:54:51.422 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (334.7 MB) - Swap: 5 GB (1.4 GB)
Jul-09 22:54:51.429 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 22:54:51.442 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 22:54:51.446 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 22:54:51.454 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 22:54:51.463 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 22:54:51.467 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 22:54:51.493 [main] DEBUG nextflow.Session - Session start
Jul-09 22:54:51.494 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_22-54-51.txt
Jul-09 22:54:51.497 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:54:51.505 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:54:51.631 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 22:54:52.204 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 22:54:52.266 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:54:52.268 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:54:52.272 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.272 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.274 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 22:54:52.277 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 22:54:52.278 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 22:54:52.286 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:54:52.308 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_single` matches labels `process_single` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX
Jul-09 22:54:52.313 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.313 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.314 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:54:52.317 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM
Jul-09 22:54:52.318 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.318 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.319 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM': maxForks=0; fair=false; array=0
Jul-09 22:54:52.322 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX
Jul-09 22:54:52.323 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.323 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.323 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:54:52.333 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:54:52.333 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:54:52.334 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.334 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.335 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 22:54:52.342 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:54:52.342 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FILTERMUTECTCALLS` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:54:52.343 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.343 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.343 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 22:54:52.349 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:54:52.350 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.350 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.350 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 22:54:52.355 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:54:52.356 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:54:52.356 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.356 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.357 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:54:52.361 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:54:52.362 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:54:52.362 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.362 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.362 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:54:52.366 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:54:52.366 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:54:52.367 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.367 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.367 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 22:54:52.372 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:54:52.373 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.373 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.373 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 22:54:52.380 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 22:54:52.380 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 22:54:52.381 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.381 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.381 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:54:52.385 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 22:54:52.385 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_ANALYZE` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 22:54:52.386 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.386 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.386 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE': maxForks=0; fair=false; array=0
Jul-09 22:54:52.390 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 22:54:52.390 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 22:54:52.390 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.390 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.391 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-09 22:54:52.396 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 22:54:52.396 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:TRACK_CLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 22:54:52.397 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.397 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.397 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES': maxForks=0; fair=false; array=0
Jul-09 22:54:52.405 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:54:52.406 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.406 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.406 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 22:54:52.410 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:54:52.410 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:54:52.410 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.410 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.411 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 22:54:52.413 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:54:52.414 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.414 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.414 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:54:52.418 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:54:52.419 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:54:52.419 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:54:52.419 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:54:52.422 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, BWA_MEM, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, BWA_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, TRACK_CLONES, PARSE_HLA_TYPES, OPTITYPE, MUTECT2, MERGE_VARIANTS, NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE, SALMON_QUANT, SAMTOOLS_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:54:52.429 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 22:54:52.429 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 22:54:52.431 [main] DEBUG nextflow.Session - Igniting dataflow network (37)
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:54:52.433 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:54:52.434 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:54:52.435 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_e25af700512e8009: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_796f98b8be950eb4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_308ddae06be8c146: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
  Script_5ee7b87706deaa67: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_08366ba8b5bdcd21: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_53e9387dcb711f87: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
Jul-09 22:54:52.435 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 22:54:52.435 [main] DEBUG nextflow.Session - Session await
Jul-09 22:54:52.508 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.509 [Task submitter] INFO  nextflow.Session - [0e/2f0d03] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N)
Jul-09 22:54:52.513 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.513 [Task submitter] INFO  nextflow.Session - [71/b59473] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:54:52.517 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.517 [Task submitter] INFO  nextflow.Session - [4c/4cf145] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:54:52.519 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.519 [Task submitter] INFO  nextflow.Session - [45/6cc665] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T)
Jul-09 22:54:52.522 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.522 [Task submitter] INFO  nextflow.Session - [e4/fa194e] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA)
Jul-09 22:54:52.525 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.525 [Task submitter] INFO  nextflow.Session - [fc/baf84d] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:54:52.527 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.527 [Task submitter] INFO  nextflow.Session - [14/3fc108] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:54:52.530 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:54:52.530 [Task submitter] INFO  nextflow.Session - [de/28b8af] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 22:54:52.917 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:54:53.043 [Actor Thread 17] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:54:53.261 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:54:53.317 [FileTransfer-1] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-1ac36788-5c90-4371-a853-9649e4d437ff/58/803415b92c3ca67cacf77186e4b399/genome.fasta
Jul-09 22:54:53.317 [FileTransfer-2] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-1ac36788-5c90-4371-a853-9649e4d437ff/e4/17f2559b9c2d62712cad3cc5809a89/genome.gtf
Jul-09 22:54:53.438 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:54:53.508 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:54:53.513 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:54:53.586 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:55:00.289 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/14/3fc108cce578aa52f21f592bf53515]
Jul-09 22:55:00.290 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:55:00.293 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:00.293 [Task submitter] INFO  nextflow.Session - [ca/bddcc5] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:55:00.315 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:55:06.280 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/fc/baf84d62d45f148ad04163eba2973c]
Jul-09 22:55:06.285 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:06.287 [Task submitter] INFO  nextflow.Session - [c2/f2bee1] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 22:55:06.335 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/4c/4cf14561d03456ef421760c61ce7a8]
Jul-09 22:55:06.336 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/71/b59473e14f10068df6bf13957aead6]
Jul-09 22:55:06.336 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/de/28b8af1af38d8791821db914cb7242]
Jul-09 22:55:06.339 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:06.340 [Task submitter] INFO  nextflow.Session - [b2/8c67ff] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX (genome.fasta)
Jul-09 22:55:06.344 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:06.344 [Task submitter] INFO  nextflow.Session - [d0/3ddf04] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta)
Jul-09 22:55:07.087 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0e/2f0d031fe26c5914e54cab832c78ec]
Jul-09 22:55:08.034 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/d0/3ddf04b95767dbe92275984b2bb69c]
Jul-09 22:55:08.045 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:08.045 [Task submitter] INFO  nextflow.Session - [28/c19b89] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)
Jul-09 22:55:08.048 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:08.048 [Task submitter] INFO  nextflow.Session - [57/637c73] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T)
Jul-09 22:55:08.544 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX (genome.fasta); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/b2/8c67ff46fab3e314c9c0d00f6075a5]
Jul-09 22:55:08.556 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:08.557 [Task submitter] INFO  nextflow.Session - [7a/ff0064] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_01_T)
Jul-09 22:55:08.563 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:08.563 [Task submitter] INFO  nextflow.Session - [f4/645f78] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_01_cfDNA)
Jul-09 22:55:09.285 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ca/bddcc5c6c0ab7eef589e40106a0642]
Jul-09 22:55:09.292 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:09.293 [Task submitter] INFO  nextflow.Session - [54/6cba81] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_01_N)
Jul-09 22:55:11.050 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/28/c19b89835e1f2b2b3ce84605811510]
Jul-09 22:55:11.052 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/57/637c731c873d6b9b0eb43cfdf4b92b]
Jul-09 22:55:11.053 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:55:11.053 [Task submitter] INFO  nextflow.Session - [93/57f4e4] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_02_T)
Jul-09 22:55:11.054 [TaskFinalizer-10] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/28/c19b89835e1f2b2b3ce84605811510
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)` terminated with an error exit status (1)
Jul-09 22:55:11.063 [TaskFinalizer-10] ERROR nextflow.processor.TaskProcessor - Error executing process > 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)'

Caused by:
  Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)` terminated with an error exit status (1)


Command executed:

  salmon quant \
      --threads 2 \
      --libType=A \
      --index salmon \
      --mates1 SAMPLE_01_T_rna_1.fastq.gz \
      --mates2 SAMPLE_01_T_rna_2.fastq.gz \
      --output SAMPLE_01_T \
  
  
  cat <<-END_VERSIONS > versions.yml
  "IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT":
      salmon: $(echo $(salmon --version) | sed -e "s/salmon //g")
  END_VERSIONS

Command exit status:
  1

Command output:
  (empty)

Command error:
  [2025-07-10 05:55:09.035] [jointLog] [info] Usage of --validateMappings implies use of minScoreFraction. Since not explicitly specified, it is being set to 0.65
  [2025-07-10 05:55:09.035] [jointLog] [info] Setting consensusSlack to selective-alignment default of 0.35.
  [2025-07-10 05:55:09.035] [jointLog] [info] parsing read library format
  [2025-07-10 05:55:09.036] [jointLog] [info] There is 1 library.
  [2025-07-10 05:55:09.057] [jointLog] [info] Loading pufferfish index
  [2025-07-10 05:55:09.060] [jointLog] [info] Loading dense pufferfish index.
  -----------------------------------------
  | Loading contig table | Time = 6.3495 ms
  -----------------------------------------
  size = 83
  -----------------------------------------
  | Loading contig offsets | Time = 4.3302 ms
  -----------------------------------------
  -----------------------------------------
  | Loading reference lengths | Time = 176.12 us
  -----------------------------------------
  -----------------------------------------
  | Loading mphf table | Time = 4.2688 ms
  -----------------------------------------
  size = 42218
  Number of ones: 82
  Number of ones per inventory item: 512
  Inventory entries filled: 1
  -----------------------------------------
  | Loading contig boundaries | Time = 3.1628 ms
  -----------------------------------------
  size = 42218
  -----------------------------------------
  | Loading sequence | Time = 4.525 ms
  -----------------------------------------
  size = 39758
  -----------------------------------------
  | Loading positions | Time = 856.42 us
  -----------------------------------------
  size = 40001
  -----------------------------------------
  | Loading reference sequence | Time = 698.46 us
  -----------------------------------------
  -----------------------------------------
  | Loading reference accumulative lengths | Time = 175.29 us
  -----------------------------------------
  [2025-07-10 05:55:09.109] [jointLog] [info] done
  [2025-07-10 05:55:09.156] [jointLog] [info] Index contained 1 targets
  [2025-07-10 05:55:09.156] [jointLog] [info] Number of decoys : 1
  [2025-07-10 05:55:09.156] [jointLog] [info] First decoy index : 0 
  
  
  
  
  [2025-07-10 05:55:09.321] [jointLog] [warning] salmon was only able to assign 0 fragments to transcripts in the index, but the minimum number of required assigned fragments (--minAssignedFrags) was 10. This could be indicative of a mismatch between the reference and sample, or a very bad sample.  You can change the --minAssignedFrags parameter to force salmon to quantify with fewer assigned fragments (must have at least 1).

Work dir:
  /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/28/c19b89835e1f2b2b3ce84605811510

Container:
  quay.io/biocontainers/salmon:1.9.0--h7e5ed60_1

Tip: view the complete command output by changing to the process work dir and entering the command `cat .command.out`
Jul-09 22:55:11.064 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/57/637c731c873d6b9b0eb43cfdf4b92b
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T)` terminated with an error exit status (1)
Jul-09 22:55:11.066 [TaskFinalizer-10] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-09 22:55:11.100 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 22:55:21.643 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/c2/f2bee16789512cc7b4b10a1992718e]
Jul-09 22:55:34.409 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/f4/645f7884c3de64483d4f7cbc6d9913]
Jul-09 22:55:34.714 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_01_N); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/54/6cba81c003da9a5d0eb95e3cfb2433]
Jul-09 22:55:34.743 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/93/57f4e4b9290b30942a69a424af8373]
Jul-09 22:55:34.898 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/7a/ff00645dd0a99c29dcb172ec9654e5]
Jul-09 22:55:36.116 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/45/6cc66557bd758b4f75e542755a9c10]
Jul-09 22:55:36.119 [TaskFinalizer-7] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/45/6cc66557bd758b4f75e542755a9c10
  error [nextflow.exception.MissingFileException]: Missing output file(s) `*.clns` expected by process `IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T)`
Jul-09 22:55:36.210 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/e4/fa194ee3d9e8ae58a4c8a797805866]
Jul-09 22:55:36.210 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 22:55:36.210 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 22:55:36.212 [TaskFinalizer-8] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/e4/fa194ee3d9e8ae58a4c8a797805866
  error [nextflow.exception.MissingFileException]: Missing output file(s) `*.clns` expected by process `IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA)`
Jul-09 22:55:36.213 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-09 22:55:36.213 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-09 22:55:36.216 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=14; failedCount=4; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=4m 45s; failedDuration=3m 3s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=8; peakCpus=16; peakMemory=48 GB; ]
Jul-09 22:55:36.216 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 22:55:36.217 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 22:55:36.758 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 22:55:36.835 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 22:55:36.999 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 22:55:37.020 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-09 22:55:37.020 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
