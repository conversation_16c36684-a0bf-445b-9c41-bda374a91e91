Jul-10 00:10:42.615 [main] DEBUG nextflow.cli.Launcher - $> nextflow run run_tcr_longitudinal.nf -profile docker,test_tcr_longitudinal
Jul-10 00:10:42.656 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-10 00:10:42.668 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-10 00:10:42.685 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-10 00:10:42.686 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-10 00:10:42.687 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-10 00:10:42.692 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-10 00:10:42.700 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-10 00:10:42.701 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-10 00:10:42.718 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-10 00:10:42.719 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@57b9e423] - activable => nextflow.secret.LocalSecretsProvider@57b9e423
Jul-10 00:10:42.722 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `docker,test_tcr_longitudinal`
Jul-10 00:10:43.137 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, test_tcr_longitudinal, singularity, aws, docker, podman]
Jul-10 00:10:43.150 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-10 00:10:43.157 [main] DEBUG nextflow.cli.CmdRun - Launching `run_tcr_longitudinal.nf` [sad_easley] DSL2 - revision: 3ca1e60532
Jul-10 00:10:43.158 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-10 00:10:43.158 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-10 00:10:43.182 [main] DEBUG nextflow.Session - Session UUID: 09581ce0-024c-4f33-8798-5ced5796a145
Jul-10 00:10:43.182 [main] DEBUG nextflow.Session - Run name: sad_easley
Jul-10 00:10:43.182 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-10 00:10:43.185 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-10 00:10:43.187 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-10 00:10:43.203 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (376.4 MB) - Swap: 5 GB (1.2 GB)
Jul-10 00:10:43.208 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-10 00:10:43.222 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-10 00:10:43.226 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-10 00:10:43.234 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-10 00:10:43.243 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-10 00:10:43.248 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-10 00:10:43.274 [main] DEBUG nextflow.Session - Session start
Jul-10 00:10:43.275 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results_tcr_longitudinal/pipeline_info/execution_trace_2025-07-10_00-10-43.txt
Jul-10 00:10:43.278 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-10 00:10:43.279 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-10 00:10:43.353 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-10 00:10:43.358 [main] INFO  nextflow.Nextflow - TCR LONGITUDINAL ANALYSIS PIPELINE
===================================
input    : assets/samplesheet_tcr_longitudinal.csv
outdir   : results_tcr_longitudinal

Jul-10 00:10:43.619 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
Jul-10 00:10:43.625 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.626 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.628 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-10 00:10:43.631 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-10 00:10:43.632 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-10 00:10:43.641 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN': maxForks=0; fair=false; array=0
Jul-10 00:10:43.654 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
Jul-10 00:10:43.655 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.655 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.656 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE': maxForks=0; fair=false; array=0
Jul-10 00:10:43.660 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
Jul-10 00:10:43.661 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.661 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.661 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER': maxForks=0; fair=false; array=0
Jul-10 00:10:43.664 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE
Jul-10 00:10:43.666 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.666 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.666 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE': maxForks=0; fair=false; array=0
Jul-10 00:10:43.669 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-10 00:10:43.669 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-10 00:10:43.671 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.671 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.671 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-10 00:10:43.678 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
Jul-10 00:10:43.679 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.679 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.680 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS': maxForks=0; fair=false; array=0
Jul-10 00:10:43.683 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN, MIXCR_ALIGN, MIXCR_COLLAPSE, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE, MIXCR_FILTER, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER, MIXCR_ASSEMBLE, IMMUNARCH_ANALYSIS, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS, MIXCR_EXPORTCLONES
Jul-10 00:10:43.692 [main] WARN  nextflow.Session - There's no process matching config selector: FASTQC
Jul-10 00:10:43.767 [main] WARN  nextflow.Session - There's no process matching config selector: MULTIQC
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: MUTECT2
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: SALMON_QUANT
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: MIXCR_ANALYZE -- Did you mean: MIXCR_ALIGN?
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: NETMHCPAN
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: OPTITYPE
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: FILTERMUTECTCALLS
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: SALMON_INDEX
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: CUSTOM_DUMPSOFTWAREVERSIONS
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: TRACK_CLONES
Jul-10 00:10:43.769 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jul-10 00:10:43.771 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
Jul-10 00:10:43.772 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_2324d938a6d547eb: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_longitudinal.nf
  Script_c91be093284aa940: /Users/<USER>/Downloads/immune_neoantigen_pipeline/run_tcr_longitudinal.nf
  Script_906d6f324b018ba3: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-10 00:10:43.772 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-10 00:10:43.772 [main] DEBUG nextflow.Session - Session await
Jul-10 00:10:43.888 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.889 [Task submitter] INFO  nextflow.Session - [55/e4ee45] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline)
Jul-10 00:10:43.894 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.894 [Task submitter] INFO  nextflow.Session - [0a/3bf56f] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1)
Jul-10 00:10:43.897 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.897 [Task submitter] INFO  nextflow.Session - [08/3008b2] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression)
Jul-10 00:10:43.900 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.900 [Task submitter] INFO  nextflow.Session - [00/b48650] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment)
Jul-10 00:10:43.903 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.904 [Task submitter] INFO  nextflow.Session - [d2/c336d3] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment)
Jul-10 00:10:43.907 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.907 [Task submitter] INFO  nextflow.Session - [9d/a0f342] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T3_progression)
Jul-10 00:10:43.911 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.911 [Task submitter] INFO  nextflow.Session - [3d/05b5b1] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1)
Jul-10 00:10:43.914 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.915 [Task submitter] INFO  nextflow.Session - [f3/7a4190] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline)
Jul-10 00:10:54.698 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/55/e4ee45b25ae01b2abaa47c295c7abd]
Jul-10 00:10:54.699 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-10 00:10:54.703 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:54.703 [Task submitter] INFO  nextflow.Session - [43/b979ae] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T2_cycle3)
Jul-10 00:10:54.709 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/55/e4ee45b25ae01b2abaa47c295c7abd
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline)` terminated with an error exit status (137)
Jul-10 00:10:54.713 [TaskFinalizer-1] INFO  nextflow.processor.TaskProcessor - [55/e4ee45] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:10:55.260 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0a/3bf56fbd4574c96a35d924c3dde3b2]
Jul-10 00:10:55.262 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0a/3bf56fbd4574c96a35d924c3dde3b2
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1)` terminated with an error exit status (137)
Jul-10 00:10:55.263 [TaskFinalizer-2] INFO  nextflow.processor.TaskProcessor - [0a/3bf56f] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:10:55.267 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:55.267 [Task submitter] INFO  nextflow.Session - [0d/e6ae32] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T2_cycle3)
Jul-10 00:10:58.271 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/08/3008b2b315086a104eeacfddf84a2c]
Jul-10 00:10:58.273 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/08/3008b2b315086a104eeacfddf84a2c
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression)` terminated with an error exit status (137)
Jul-10 00:10:58.274 [TaskFinalizer-3] INFO  nextflow.processor.TaskProcessor - [08/3008b2] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:10:58.277 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:58.277 [Task submitter] INFO  nextflow.Session - [d0/03fc8e] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline)
Jul-10 00:10:58.401 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/f3/7a4190abcbe4ba45bf5354a140c1f1]
Jul-10 00:10:58.402 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/f3/7a4190abcbe4ba45bf5354a140c1f1
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline)` terminated with an error exit status (137)
Jul-10 00:10:58.403 [TaskFinalizer-4] INFO  nextflow.processor.TaskProcessor - [f3/7a4190] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:10:58.405 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:58.405 [Task submitter] INFO  nextflow.Session - [b4/78e780] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1)
Jul-10 00:11:01.107 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/3d/05b5b1280040ec285325f907d5e956]
Jul-10 00:11:01.109 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/3d/05b5b1280040ec285325f907d5e956
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1)` terminated with an error exit status (137)
Jul-10 00:11:01.110 [TaskFinalizer-5] INFO  nextflow.processor.TaskProcessor - [3d/05b5b1] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:11:01.112 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:11:01.113 [Task submitter] INFO  nextflow.Session - [36/70054e] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression)
Jul-10 00:11:07.370 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/00/b4865066977ee4fb57d6de26c951b7]
Jul-10 00:11:07.372 [TaskFinalizer-6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/00/b4865066977ee4fb57d6de26c951b7
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment)` terminated with an error exit status (137)
Jul-10 00:11:07.373 [TaskFinalizer-6] INFO  nextflow.processor.TaskProcessor - [00/b48650] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:11:07.374 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:11:07.374 [Task submitter] INFO  nextflow.Session - [89/360c1c] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline)
Jul-10 00:11:19.625 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment); status: COMPLETED; exit: 137; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/d2/c336d3e8afb8c10ce4fa4a9825d3a1]
Jul-10 00:11:19.626 [TaskFinalizer-7] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/d2/c336d3e8afb8c10ce4fa4a9825d3a1
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment)` terminated with an error exit status (137)
Jul-10 00:11:19.627 [TaskFinalizer-7] INFO  nextflow.processor.TaskProcessor - [d2/c336d3] NOTE: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment)` terminated with an error exit status (137) -- Execution is retried (1)
Jul-10 00:11:19.631 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:11:19.632 [Task submitter] INFO  nextflow.Session - [9e/bc15c3] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1)
Jul-10 00:11:34.967 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T3_progression); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/9d/a0f34262d1c4988c8662f39e34e93c]
Jul-10 00:11:34.972 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:11:34.972 [Task submitter] INFO  nextflow.Session - [25/113fbb] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment)
Jul-10 00:11:34.998 [TaskFinalizer-8] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-10 00:11:58.002 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/d0/03fc8e835d34207ecc906b59c68a01]
Jul-10 00:11:58.011 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:11:58.011 [Task submitter] INFO  nextflow.Session - [35/0cfefb] Re-submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment)
Jul-10 00:11:59.040 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/b4/78e7802e2eafca2bbcb63d9225dd82]
Jul-10 00:11:59.043 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:11:59.043 [Task submitter] INFO  nextflow.Session - [3c/d6e38c] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_01_T3_progression)
Jul-10 00:12:07.499 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_01_T3_progression); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/3c/d6e38cee99f063b10105463577d702]
Jul-10 00:12:07.505 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:12:07.505 [Task submitter] INFO  nextflow.Session - [37/bc2b8c] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_02_T0_baseline)
Jul-10 00:12:13.894 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/9e/bc15c335192c1225426062129a587e]
Jul-10 00:12:13.900 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:12:13.900 [Task submitter] INFO  nextflow.Session - [28/5687c9] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_01_T1_cycle1)
Jul-10 00:12:15.468 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_02_T0_baseline); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/37/bc2b8cdad859e59f5a6ad30ffa93c1]
Jul-10 00:12:15.471 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:12:15.471 [Task submitter] INFO  nextflow.Session - [a0/8c4af6] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER (PATIENT_01_T3_progression)
Jul-10 00:12:18.733 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER (PATIENT_01_T3_progression); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/a0/8c4af6ae2793d77e3239b56e359bdf]
Jul-10 00:12:18.734 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER (PATIENT_01_T3_progression); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/a0/8c4af6ae2793d77e3239b56e359bdf
  error [nextflow.exception.ProcessFailedException]: Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER (PATIENT_01_T3_progression)` terminated with an error exit status (1)
Jul-10 00:12:18.736 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:12:18.736 [Task submitter] INFO  nextflow.Session - [42/aa50f2] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_02_T1_cycle1)
Jul-10 00:12:18.741 [TaskFinalizer-4] ERROR nextflow.processor.TaskProcessor - Error executing process > 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER (PATIENT_01_T3_progression)'

Caused by:
  Process `NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER (PATIENT_01_T3_progression)` terminated with an error exit status (1)


Command executed:

  # Filter clones by count and frequency
  mixcr filter \
      --min-count 2 \
      --min-fraction 0.00001 \
      --report PATIENT_01_T3_progression_filter.report \
       \
      PATIENT_01_T3_progression.clns \
      PATIENT_01_T3_progression_filtered.clns
  
  cat <<-END_VERSIONS > versions.yml
  "NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER":
      mixcr: $(mixcr --version 2>&1 | grep -o 'MiXCR v[0-9.]*' | sed 's/MiXCR v//')
  END_VERSIONS

Command exit status:
  1

Command output:
  (empty)

Command error:
  WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
  Unmatched arguments: filter, --min-count, 2, --min-fraction, 0.00001, --report, PATIENT_01_T3_progression_filter.report, PATIENT_01_T3_progression.clns, PATIENT_01_T3_progression_filtered.clns
  Did you mean: filterAlignments or extend or versionInfo?

Work dir:
  /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/a0/8c4af6ae2793d77e3239b56e359bdf

Container:
  mgibio/mixcr:latest

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
Jul-10 00:12:18.743 [TaskFinalizer-4] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-10 00:12:18.777 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-10 00:12:21.655 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_01_T1_cycle1); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/28/5687c9edf792207001bc8554325083]
Jul-10 00:12:23.405 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T2_cycle3); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/43/b979aefec0d9ff4c7dec3a914d75cb]
Jul-10 00:12:25.730 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE (PATIENT_02_T1_cycle1); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/42/aa50f20fcf0e7e3fd05bd22194ecb8]
Jul-10 00:12:28.466 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/25/113fbb0cfb96182f68a868797e7290]
Jul-10 00:12:37.458 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T2_cycle3); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/0d/e6ae32b6c6c58f344be999e5d37ee0]
Jul-10 00:12:39.249 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/35/0cfefb04b76054b1c518f8b9f8d466]
Jul-10 00:12:40.996 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T0_baseline); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/89/360c1cb4a6f147c073c514bdeb423c]
Jul-10 00:12:43.701 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/36/70054e4b5c8bffc89242d72d6aba13]
Jul-10 00:12:43.702 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-10 00:12:43.702 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-10 00:12:43.705 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-10 00:12:43.705 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-10 00:12:43.708 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=14; failedCount=8; ignoredCount=0; cachedCount=0; pendingCount=1; submittedCount=0; runningCount=0; retriesCount=7; abortedCount=0; succeedDuration=24m; failedDuration=4m 20s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=8; peakCpus=16; peakMemory=48 GB; ]
Jul-10 00:12:43.708 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-10 00:12:43.708 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-10 00:12:44.343 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-10 00:12:44.411 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-10 00:12:44.549 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-10 00:12:44.559 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-10 00:12:44.559 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
