Jul-10 00:10:42.615 [main] DEBUG nextflow.cli.Launcher - $> nextflow run run_tcr_longitudinal.nf -profile docker,test_tcr_longitudinal
Jul-10 00:10:42.656 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-10 00:10:42.668 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-10 00:10:42.685 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-10 00:10:42.686 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-10 00:10:42.687 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-10 00:10:42.692 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-10 00:10:42.700 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-10 00:10:42.701 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-10 00:10:42.718 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-10 00:10:42.719 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@57b9e423] - activable => nextflow.secret.LocalSecretsProvider@57b9e423
Jul-10 00:10:42.722 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `docker,test_tcr_longitudinal`
Jul-10 00:10:43.137 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, test_tcr_longitudinal, singularity, aws, docker, podman]
Jul-10 00:10:43.150 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-10 00:10:43.157 [main] DEBUG nextflow.cli.CmdRun - Launching `run_tcr_longitudinal.nf` [sad_easley] DSL2 - revision: 3ca1e60532
Jul-10 00:10:43.158 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-10 00:10:43.158 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-10 00:10:43.182 [main] DEBUG nextflow.Session - Session UUID: 09581ce0-024c-4f33-8798-5ced5796a145
Jul-10 00:10:43.182 [main] DEBUG nextflow.Session - Run name: sad_easley
Jul-10 00:10:43.182 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-10 00:10:43.185 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-10 00:10:43.187 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-10 00:10:43.203 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (376.4 MB) - Swap: 5 GB (1.2 GB)
Jul-10 00:10:43.208 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-10 00:10:43.222 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-10 00:10:43.226 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-10 00:10:43.234 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-10 00:10:43.243 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-10 00:10:43.248 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-10 00:10:43.274 [main] DEBUG nextflow.Session - Session start
Jul-10 00:10:43.275 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results_tcr_longitudinal/pipeline_info/execution_trace_2025-07-10_00-10-43.txt
Jul-10 00:10:43.278 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-10 00:10:43.279 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-10 00:10:43.353 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-10 00:10:43.358 [main] INFO  nextflow.Nextflow - TCR LONGITUDINAL ANALYSIS PIPELINE
===================================
input    : assets/samplesheet_tcr_longitudinal.csv
outdir   : results_tcr_longitudinal

Jul-10 00:10:43.619 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
Jul-10 00:10:43.625 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.626 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.628 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-10 00:10:43.631 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-10 00:10:43.632 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-10 00:10:43.641 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN': maxForks=0; fair=false; array=0
Jul-10 00:10:43.654 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
Jul-10 00:10:43.655 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.655 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.656 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE': maxForks=0; fair=false; array=0
Jul-10 00:10:43.660 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
Jul-10 00:10:43.661 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.661 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.661 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER': maxForks=0; fair=false; array=0
Jul-10 00:10:43.664 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE
Jul-10 00:10:43.666 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.666 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.666 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE': maxForks=0; fair=false; array=0
Jul-10 00:10:43.669 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-10 00:10:43.669 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-10 00:10:43.671 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.671 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.671 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-10 00:10:43.678 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
Jul-10 00:10:43.679 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-10 00:10:43.679 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-10 00:10:43.680 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS': maxForks=0; fair=false; array=0
Jul-10 00:10:43.683 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN, MIXCR_ALIGN, MIXCR_COLLAPSE, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE, MIXCR_FILTER, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER, MIXCR_ASSEMBLE, IMMUNARCH_ANALYSIS, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS, MIXCR_EXPORTCLONES
Jul-10 00:10:43.692 [main] WARN  nextflow.Session - There's no process matching config selector: FASTQC
Jul-10 00:10:43.767 [main] WARN  nextflow.Session - There's no process matching config selector: MULTIQC
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: MUTECT2
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: SALMON_QUANT
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: MIXCR_ANALYZE -- Did you mean: MIXCR_ALIGN?
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: NETMHCPAN
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: OPTITYPE
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: FILTERMUTECTCALLS
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: SALMON_INDEX
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: CUSTOM_DUMPSOFTWAREVERSIONS
Jul-10 00:10:43.768 [main] WARN  nextflow.Session - There's no process matching config selector: TRACK_CLONES
Jul-10 00:10:43.769 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jul-10 00:10:43.771 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_COLLAPSE
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-10 00:10:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
Jul-10 00:10:43.772 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_2324d938a6d547eb: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_longitudinal.nf
  Script_c91be093284aa940: /Users/<USER>/Downloads/immune_neoantigen_pipeline/run_tcr_longitudinal.nf
  Script_906d6f324b018ba3: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-10 00:10:43.772 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-10 00:10:43.772 [main] DEBUG nextflow.Session - Session await
Jul-10 00:10:43.888 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.889 [Task submitter] INFO  nextflow.Session - [55/e4ee45] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T0_baseline)
Jul-10 00:10:43.894 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.894 [Task submitter] INFO  nextflow.Session - [0a/3bf56f] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T1_cycle1)
Jul-10 00:10:43.897 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.897 [Task submitter] INFO  nextflow.Session - [08/3008b2] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T3_progression)
Jul-10 00:10:43.900 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.900 [Task submitter] INFO  nextflow.Session - [00/b48650] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T4_posttreatment)
Jul-10 00:10:43.903 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.904 [Task submitter] INFO  nextflow.Session - [d2/c336d3] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T4_posttreatment)
Jul-10 00:10:43.907 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.907 [Task submitter] INFO  nextflow.Session - [9d/a0f342] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_01_T3_progression)
Jul-10 00:10:43.911 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-10 00:10:43.911 [Task submitter] INFO  nextflow.Session - [3d/05b5b1] Submitted process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN (PATIENT_02_T1_cycle1)
