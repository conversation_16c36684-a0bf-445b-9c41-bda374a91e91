Jul-09 23:05:24.403 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 23:05:24.428 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 23:05:24.440 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 23:05:24.454 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 23:05:24.455 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 23:05:24.456 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 23:05:24.460 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 23:05:24.468 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 23:05:24.469 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 23:05:24.484 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 23:05:24.486 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@21ab988f] - activable => nextflow.secret.LocalSecretsProvider@21ab988f
Jul-09 23:05:24.487 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 23:05:24.912 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 23:05:24.925 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 23:05:24.932 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [stupefied_bassi] DSL2 - revision: 149844d342
Jul-09 23:05:24.932 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 23:05:24.932 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 23:05:24.953 [main] DEBUG nextflow.Session - Session UUID: 58c1eb7c-1b8c-4704-8c38-15d65f47fdf8
Jul-09 23:05:24.953 [main] DEBUG nextflow.Session - Run name: stupefied_bassi
Jul-09 23:05:24.953 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 23:05:24.956 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 23:05:24.957 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 23:05:24.968 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (119.6 MB) - Swap: 5 GB (1.2 GB)
Jul-09 23:05:24.974 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 23:05:24.986 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 23:05:24.989 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 23:05:24.997 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 23:05:25.007 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 23:05:25.010 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 23:05:25.033 [main] DEBUG nextflow.Session - Session start
Jul-09 23:05:25.034 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_23-05-24.txt
Jul-09 23:05:25.036 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 23:05:25.037 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 23:05:25.164 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 23:05:25.731 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 23:05:25.795 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 23:05:25.796 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 23:05:25.800 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.800 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.803 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 23:05:25.805 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 23:05:25.806 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 23:05:25.813 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 23:05:25.833 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_single` matches labels `process_single` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX
Jul-09 23:05:25.838 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.839 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.839 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX': maxForks=0; fair=false; array=0
Jul-09 23:05:25.843 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM
Jul-09 23:05:25.844 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.844 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.844 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM': maxForks=0; fair=false; array=0
Jul-09 23:05:25.847 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX
Jul-09 23:05:25.848 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.848 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.849 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX': maxForks=0; fair=false; array=0
Jul-09 23:05:25.859 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 23:05:25.859 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 23:05:25.861 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.861 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.861 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 23:05:25.868 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 23:05:25.868 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FILTERMUTECTCALLS` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 23:05:25.869 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.869 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.870 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 23:05:25.874 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 23:05:25.874 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:OPTITYPE` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 23:05:25.875 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.875 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.875 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE': maxForks=0; fair=false; array=0
Jul-09 23:05:25.881 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 23:05:25.882 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.882 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.882 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 23:05:25.888 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 23:05:25.888 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 23:05:25.889 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.889 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.889 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 23:05:25.894 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 23:05:25.895 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 23:05:25.895 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.895 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.896 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 23:05:25.904 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 23:05:25.905 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 23:05:25.906 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.906 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.906 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 23:05:25.915 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 23:05:25.916 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.916 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.916 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 23:05:25.926 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 23:05:25.926 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 23:05:25.928 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.928 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.928 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 23:05:25.935 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 23:05:25.935 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_ANALYZE` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 23:05:25.936 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.936 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.936 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE': maxForks=0; fair=false; array=0
Jul-09 23:05:25.941 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 23:05:25.941 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 23:05:25.942 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.942 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.942 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-09 23:05:25.947 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 23:05:25.948 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:TRACK_CLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 23:05:25.949 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.949 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.949 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES': maxForks=0; fair=false; array=0
Jul-09 23:05:25.958 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 23:05:25.959 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.959 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.959 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 23:05:25.963 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 23:05:25.964 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 23:05:25.964 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.964 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.965 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 23:05:25.968 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 23:05:25.969 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.969 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.969 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 23:05:25.975 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 23:05:25.975 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:05:25.975 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:05:25.976 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 23:05:25.981 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, BWA_MEM, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, BWA_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, PARSE_HLA_TYPES, TRACK_CLONES, OPTITYPE, NETMHCPAN, MUTECT2, MERGE_VARIANTS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE, SALMON_QUANT, SAMTOOLS_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 23:05:25.987 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 23:05:25.988 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 23:05:25.989 [main] DEBUG nextflow.Session - Igniting dataflow network (37)
Jul-09 23:05:25.991 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 23:05:25.991 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX
Jul-09 23:05:25.991 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 23:05:25.992 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 23:05:25.993 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 23:05:25.993 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 23:05:25.993 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 23:05:25.993 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_796f98b8be950eb4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_308ddae06be8c146: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
  Script_5ee7b87706deaa67: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_08366ba8b5bdcd21: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_8e63bd0ab8129302: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_53e9387dcb711f87: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
Jul-09 23:05:25.993 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 23:05:25.993 [main] DEBUG nextflow.Session - Session await
Jul-09 23:05:26.079 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.080 [Task submitter] INFO  nextflow.Session - [cc/c42baa] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T)
Jul-09 23:05:26.084 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.084 [Task submitter] INFO  nextflow.Session - [ae/97aff8] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA)
Jul-09 23:05:26.087 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.087 [Task submitter] INFO  nextflow.Session - [2f/87d96c] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)
Jul-09 23:05:26.090 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.090 [Task submitter] INFO  nextflow.Session - [69/7c8668] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA)
Jul-09 23:05:26.093 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.093 [Task submitter] INFO  nextflow.Session - [73/3b1ce3] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T)
Jul-09 23:05:26.095 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.095 [Task submitter] INFO  nextflow.Session - [5c/f90e3d] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T)
Jul-09 23:05:26.102 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.103 [Task submitter] INFO  nextflow.Session - [4c/5375d8] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 23:05:26.106 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:26.106 [Task submitter] INFO  nextflow.Session - [68/50f22e] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N)
Jul-09 23:05:26.679 [Actor Thread 10] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 23:05:27.235 [Actor Thread 13] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 23:05:27.286 [FileTransfer-1] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-58c1eb7c-1b8c-4704-8c38-15d65f47fdf8/46/676a532c7b6bdd651f251533d2fc6e/genome.fasta
Jul-09 23:05:27.715 [Actor Thread 13] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 23:05:27.755 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 23:05:27.760 [FileTransfer-2] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-58c1eb7c-1b8c-4704-8c38-15d65f47fdf8/3d/9353b77422884a73b5c436514389d9/genome.gtf
Jul-09 23:05:27.804 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 23:05:27.930 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/2f/87d96c89961fe9dfdae94305c0a301]
Jul-09 23:05:27.931 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 23:05:27.935 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:27.935 [Task submitter] INFO  nextflow.Session - [4d/424bec] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 23:05:27.938 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/2f/87d96c89961fe9dfdae94305c0a301
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)` terminated with an error exit status (2)
Jul-09 23:05:27.946 [TaskFinalizer-1] ERROR nextflow.processor.TaskProcessor - Error executing process > 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)'

Caused by:
  Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)` terminated with an error exit status (2)


Command executed:

  OptiTypePipeline.py \
      --input SAMPLE_01_N_wes_1.fastq.gz SAMPLE_01_N_wes_2.fastq.gz \
      --dna \
      --prefix SAMPLE_01_N \
      --outdir . \
      --verbose
  
  cat <<-END_VERSIONS > versions.yml
  "IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE":
      optitype: $(OptiTypePipeline.py --version 2>&1 | grep -o 'OptiType [0-9.]*' | cut -d' ' -f2)
  END_VERSIONS

Command exit status:
  2

Command output:
  (empty)

Command error:
  WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
  usage: OptiType [-h] --input FQ [FQ ...] (--rna | --dna) [--beta B]
                  [--enumerate N] --outdir OUTDIR [--prefix PREFIX] [--verbose]
                  [--config CONFIG]
  OptiType: error: argument --config/-c: can't open 'eval export PYTHONNOUSERSITE="1"
  export R_PROFILE_USER="/.Rprofile"
  export R_ENVIRON_USER="/.Renviron"
  export JULIA_DEPOT_PATH="/usr/local/share/julia"
  export PATH="$PATH:/Users/<USER>/Downloads/immune_neoantigen_pipeline/bin"; /bin/bash -euo pipefail .command.run nxf_trace': [Errno 2] No such file or directory: 'eval export PYTHONNOUSERSITE="1"\nexport R_PROFILE_USER="/.Rprofile"\nexport R_ENVIRON_USER="/.Renviron"\nexport JULIA_DEPOT_PATH="/usr/local/share/julia"\nexport PATH="$PATH:/Users/<USER>/Downloads/immune_neoantigen_pipeline/bin"; /bin/bash -euo pipefail .command.run nxf_trace'

Work dir:
  /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/2f/87d96c89961fe9dfdae94305c0a301

Container:
  fred2/optitype:latest

Tip: when you have fixed the problem you can continue the execution adding the option `-resume` to the run command line
Jul-09 23:05:27.950 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/5c/f90e3d22e99dc345a0b8fae73acb25]
Jul-09 23:05:27.951 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/5c/f90e3d22e99dc345a0b8fae73acb25
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_T)` terminated with an error exit status (2)
Jul-09 23:05:27.953 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 23:05:27.954 [Task submitter] INFO  nextflow.Session - [6c/8e000b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 23:05:27.954 [TaskFinalizer-1] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-09 23:05:27.982 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 23:05:27.983 [Actor Thread 6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 23:05:27.986 [Actor Thread 15] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 23:05:28.015 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ae/97aff891721d1975fbe13855e1c819]
Jul-09 23:05:28.016 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ae/97aff891721d1975fbe13855e1c819
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_cfDNA)` terminated with an error exit status (2)
Jul-09 23:05:28.077 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T); status: COMPLETED; exit: 2; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/73/3b1ce30942150eef6d124db929d2a7]
Jul-09 23:05:28.078 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/73/3b1ce30942150eef6d124db929d2a7
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_02_T)` terminated with an error exit status (2)
Jul-09 23:05:28.275 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 23:05:28.283 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 23:05:37.759 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/68/50f22e8652a96ba3b016205399baf0]
Jul-09 23:05:37.778 [TaskFinalizer-5] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 23:05:38.577 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/4c/5375d877b9f5503588952de6adba53]
Jul-09 23:05:40.646 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/6c/8e000bf0606898d2a74d744b8dbcde]
Jul-09 23:05:40.649 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/4d/424beca41da411eb40113f3494aec8]
Jul-09 23:05:59.022 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/69/7c86681085e8ff3c93709776665fb2]
Jul-09 23:05:59.025 [TaskFinalizer-9] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/69/7c86681085e8ff3c93709776665fb2
  error [nextflow.exception.MissingFileException]: Missing output file(s) `*.clns` expected by process `IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_cfDNA)`
Jul-09 23:05:59.281 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/cc/c42baa5fc8be6d130dc3c3763a60a5]
Jul-09 23:05:59.282 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 23:05:59.282 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 23:05:59.283 [TaskFinalizer-10] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/cc/c42baa5fc8be6d130dc3c3763a60a5
  error [nextflow.exception.MissingFileException]: Missing output file(s) `*.clns` expected by process `IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE (SAMPLE_01_T)`
Jul-09 23:05:59.285 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-09 23:05:59.285 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-09 23:05:59.289 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=4; failedCount=6; ignoredCount=0; cachedCount=0; pendingCount=6; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=1m 26s; failedDuration=2m 23s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=8; peakCpus=16; peakMemory=48 GB; ]
Jul-09 23:05:59.289 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 23:05:59.289 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 23:05:59.815 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 23:05:59.900 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 23:05:59.978 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 23:05:59.990 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-09 23:05:59.991 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
