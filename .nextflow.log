Jul-09 22:42:19.506 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker --run_tcr false
Jul-09 22:42:19.539 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 22:42:19.553 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 22:42:19.569 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 22:42:19.570 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 22:42:19.571 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 22:42:19.576 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 22:42:19.585 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:42:19.586 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 22:42:19.602 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 22:42:19.603 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@62ddd21b] - activable => nextflow.secret.LocalSecretsProvider@62ddd21b
Jul-09 22:42:19.608 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 22:42:20.049 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 22:42:20.065 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 22:42:20.073 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [nauseous_wozniak] DSL2 - revision: 149844d342
Jul-09 22:42:20.073 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 22:42:20.073 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 22:42:20.098 [main] DEBUG nextflow.Session - Session UUID: 460ad1fd-a849-4428-9059-08d3826ceddb
Jul-09 22:42:20.098 [main] DEBUG nextflow.Session - Run name: nauseous_wozniak
Jul-09 22:42:20.098 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 22:42:20.101 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 22:42:20.103 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:42:20.117 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (6.6 GB) - Swap: 5 GB (1.4 GB)
Jul-09 22:42:20.124 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 22:42:20.137 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 22:42:20.140 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 22:42:20.148 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 22:42:20.157 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 22:42:20.162 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 22:42:20.187 [main] DEBUG nextflow.Session - Session start
Jul-09 22:42:20.189 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_22-42-20.txt
Jul-09 22:42:20.197 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:42:20.198 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 22:42:20.330 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 22:42:20.873 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 22:42:20.944 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:42:20.945 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:42:20.949 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:20.949 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:20.953 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 22:42:20.956 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 22:42:20.957 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 22:42:20.965 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:42:20.992 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_single` matches labels `process_single` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX
Jul-09 22:42:20.993 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:20.993 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:20.993 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:42:21.002 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM
Jul-09 22:42:21.003 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.003 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.003 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM': maxForks=0; fair=false; array=0
Jul-09 22:42:21.007 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX
Jul-09 22:42:21.008 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.008 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.009 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:42:21.019 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:42:21.019 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:42:21.021 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.021 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.021 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 22:42:21.028 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:42:21.028 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FILTERMUTECTCALLS` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:42:21.029 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.029 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.030 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 22:42:21.035 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:42:21.036 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.037 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.037 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 22:42:21.042 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:42:21.042 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:42:21.043 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.043 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.044 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 22:42:21.048 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:42:21.048 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:42:21.049 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.049 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.049 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 22:42:21.053 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:42:21.053 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:42:21.054 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.054 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.054 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 22:42:21.059 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:42:21.059 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.059 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.060 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 22:42:21.067 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:42:21.068 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.068 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.068 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 22:42:21.071 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:42:21.072 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:42:21.073 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.073 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.073 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 22:42:21.077 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:42:21.077 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.078 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.078 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:42:21.082 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:42:21.083 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 22:42:21.084 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 22:42:21.084 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 22:42:21.087 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, BWA_MEM, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, BWA_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, TRACK_CLONES, PARSE_HLA_TYPES, OPTITYPE, MUTECT2, MERGE_VARIANTS, NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, SALMON_QUANT, SAMTOOLS_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:42:21.093 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 22:42:21.093 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 22:42:21.094 [main] DEBUG nextflow.Session - Igniting dataflow network (32)
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 22:42:21.097 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 22:42:21.098 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 22:42:21.098 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 22:42:21.098 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 22:42:21.098 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 22:42:21.098 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_796f98b8be950eb4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
  Script_e25af700512e8009: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_5ee7b87706deaa67: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_08366ba8b5bdcd21: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_53e9387dcb711f87: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
  Script_5e083e27b34891ea: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-09 22:42:21.099 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 22:42:21.099 [main] DEBUG nextflow.Session - Session await
Jul-09 22:42:21.187 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:21.188 [Task submitter] INFO  nextflow.Session - [ef/a05dcf] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA)
Jul-09 22:42:21.193 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:21.194 [Task submitter] INFO  nextflow.Session - [bf/486e61] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:42:21.197 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:21.197 [Task submitter] INFO  nextflow.Session - [9e/0eb1a7] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T)
Jul-09 22:42:21.200 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:21.200 [Task submitter] INFO  nextflow.Session - [de/ed9b23] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:42:21.202 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:21.203 [Task submitter] INFO  nextflow.Session - [f3/f6d7ed] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T)
Jul-09 22:42:21.205 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:21.206 [Task submitter] INFO  nextflow.Session - [66/3d6974] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N)
Jul-09 22:42:21.602 [Actor Thread 11] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:42:21.842 [Actor Thread 18] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:42:22.122 [Actor Thread 11] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:42:22.340 [FileTransfer-1] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-460ad1fd-a849-4428-9059-08d3826ceddb/24/06f291fdec9bfc0f5516c0fdb630f9/genome.fasta
Jul-09 22:42:22.340 [FileTransfer-2] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-460ad1fd-a849-4428-9059-08d3826ceddb/9e/cb55b71e46a4d549a63a74ea7cc811/genome.gtf
Jul-09 22:42:22.377 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:42:22.440 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:42:22.453 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 22:42:22.518 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 22:42:22.548 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:22.549 [Task submitter] INFO  nextflow.Session - [12/839213] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX (genome.fasta)
Jul-09 22:42:22.651 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:22.652 [Task submitter] INFO  nextflow.Session - [ab/f33313] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta)
Jul-09 22:42:23.780 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ab/f33313f26839c104de882aaede482e]
Jul-09 22:42:23.781 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:42:23.790 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 22:42:23.809 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:23.809 [Task submitter] INFO  nextflow.Session - [1b/9c7678] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)
Jul-09 22:42:24.652 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/1b/9c7678c642a9a7b221b53ab93a0d11]
Jul-09 22:42:24.655 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 22:42:24.655 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/1b/9c7678c642a9a7b221b53ab93a0d11
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)` terminated with an error exit status (1)
Jul-09 22:42:24.655 [Task submitter] INFO  nextflow.Session - [65/be56ef] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T)
Jul-09 22:42:24.663 [TaskFinalizer-2] ERROR nextflow.processor.TaskProcessor - Error executing process > 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)'

Caused by:
  Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_01_T)` terminated with an error exit status (1)


Command executed:

  salmon quant \
      --threads 2 \
      --libType=A \
      --index salmon \
      --mates1 SAMPLE_01_T_rna_1.fastq.gz \
      --mates2 SAMPLE_01_T_rna_2.fastq.gz \
      --output SAMPLE_01_T \
  
  
  cat <<-END_VERSIONS > versions.yml
  "IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT":
      salmon: $(echo $(salmon --version) | sed -e "s/salmon //g")
  END_VERSIONS

Command exit status:
  1

Command output:
  (empty)

Command error:
  [2025-07-10 05:42:24.341] [jointLog] [info] Usage of --validateMappings implies use of minScoreFraction. Since not explicitly specified, it is being set to 0.65
  [2025-07-10 05:42:24.341] [jointLog] [info] Setting consensusSlack to selective-alignment default of 0.35.
  [2025-07-10 05:42:24.341] [jointLog] [info] parsing read library format
  [2025-07-10 05:42:24.343] [jointLog] [info] There is 1 library.
  -----------------------------------------
  | Loading contig table | Time = 1.4223 ms
  -----------------------------------------
  size = 83
  -----------------------------------------
  | Loading contig offsets | Time = 1.397 ms
  -----------------------------------------
  -----------------------------------------
  | Loading reference lengths | Time = 193.79 us
  -----------------------------------------
  -----------------------------------------
  | Loading mphf table | Time = 2.07 ms
  -----------------------------------------
  size = 42218
  Number of ones: 82
  Number of ones per inventory item: 512
  Inventory entries filled: 1
  -----------------------------------------
  | Loading contig boundaries | Time = 1.3447 ms
  -----------------------------------------
  size = 42218
  -----------------------------------------
  | Loading sequence | Time = 776.88 us
  -----------------------------------------
  size = 39758
  -----------------------------------------
  | Loading positions | Time = 1.4847 ms
  -----------------------------------------
  size = 40001
  -----------------------------------------
  | Loading reference sequence | Time = 940.21 us
  -----------------------------------------
  -----------------------------------------
  | Loading reference accumulative lengths | Time = 86 us
  -----------------------------------------
  [2025-07-10 05:42:24.361] [jointLog] [info] Loading pufferfish index
  [2025-07-10 05:42:24.362] [jointLog] [info] Loading dense pufferfish index.
  [2025-07-10 05:42:24.377] [jointLog] [info] done
  [2025-07-10 05:42:24.415] [jointLog] [info] Index contained 1 targets
  [2025-07-10 05:42:24.416] [jointLog] [info] Number of decoys : 1
  [2025-07-10 05:42:24.416] [jointLog] [info] First decoy index : 0 
  
  
  
  
  [2025-07-10 05:42:24.494] [jointLog] [warning] salmon was only able to assign 0 fragments to transcripts in the index, but the minimum number of required assigned fragments (--minAssignedFrags) was 10. This could be indicative of a mismatch between the reference and sample, or a very bad sample.  You can change the --minAssignedFrags parameter to force salmon to quantify with fewer assigned fragments (must have at least 1).

Work dir:
  /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/1b/9c7678c642a9a7b221b53ab93a0d11

Container:
  quay.io/biocontainers/salmon:1.9.0--h7e5ed60_1

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
Jul-09 22:42:24.665 [TaskFinalizer-2] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-09 22:42:24.667 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 22:42:26.167 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/65/be56ef5ffb6eb9f0a4ea9c6d5a89fc]
Jul-09 22:42:26.169 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/65/be56ef5ffb6eb9f0a4ea9c6d5a89fc
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT (SAMPLE_02_T)` terminated with an error exit status (1)
Jul-09 22:42:29.618 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/f3/f6d7eda0de3dcc7a256e92fec87e86]
Jul-09 22:42:29.702 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/9e/0eb1a72114becfe8ba0c903941853a]
Jul-09 22:42:29.805 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX (genome.fasta); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/12/839213861e73be1a3a4373385340b8]
Jul-09 22:42:33.566 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_02_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/de/ed9b23bce1fd94de4860fbf70ff24a]
Jul-09 22:42:33.587 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_T); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/bf/486e61cad3c979f73e567f5666a8c8]
Jul-09 22:42:34.621 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_cfDNA); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ef/a05dcfa2ae2403246f83aa2eaf26f9]
Jul-09 22:42:34.645 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/66/3d69745ae90a5f1f4bab75ce4516c2]
Jul-09 22:42:34.646 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 22:42:34.646 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 22:42:34.649 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-09 22:42:34.649 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-09 22:42:34.651 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=8; failedCount=2; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=1m 48s; failedDuration=4.5s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=8; peakCpus=15; peakMemory=48 GB; ]
Jul-09 22:42:34.651 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 22:42:34.651 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 22:42:35.131 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 22:42:35.208 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 22:42:35.327 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 22:42:35.339 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-09 22:42:35.339 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
