Jul-09 21:55:26.803 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 21:55:26.840 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 21:55:26.851 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 21:55:26.868 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 21:55:26.868 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 21:55:26.869 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 21:55:26.874 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 21:55:26.881 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 21:55:26.882 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 21:55:26.900 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 21:55:26.901 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@57b9e423] - activable => nextflow.secret.LocalSecretsProvider@57b9e423
Jul-09 21:55:26.904 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 21:55:27.341 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 21:55:27.355 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 21:55:27.362 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [shrivelled_curran] DSL2 - revision: f1e8f6bc25
Jul-09 21:55:27.362 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 21:55:27.363 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 21:55:27.386 [main] DEBUG nextflow.Session - Session UUID: 4fc15035-4fe2-4736-af40-4bbfe7d60552
Jul-09 21:55:27.386 [main] DEBUG nextflow.Session - Run name: shrivelled_curran
Jul-09 21:55:27.386 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 21:55:27.389 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 21:55:27.391 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 21:55:27.407 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (71 MB) - Swap: 5 GB (1.2 GB)
Jul-09 21:55:27.413 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 21:55:27.426 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 21:55:27.429 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 21:55:27.437 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 21:55:27.446 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 21:55:27.451 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 21:55:27.476 [main] DEBUG nextflow.Session - Session start
Jul-09 21:55:27.478 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_21-55-27.txt
Jul-09 21:55:27.481 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 21:55:27.482 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 21:55:27.623 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 21:55:28.172 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 21:55:28.235 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 21:55:28.236 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 21:55:28.240 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.240 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.243 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 21:55:28.245 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 21:55:28.246 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 21:55:28.254 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 21:55:28.280 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 21:55:28.281 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 21:55:28.282 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.282 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.282 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 21:55:28.292 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 21:55:28.293 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FILTERMUTECTCALLS` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 21:55:28.293 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.294 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.294 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 21:55:28.299 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 21:55:28.299 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:OPTITYPE` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 21:55:28.300 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.300 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.301 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE': maxForks=0; fair=false; array=0
Jul-09 21:55:28.311 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 21:55:28.313 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.313 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.314 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 21:55:28.319 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:55:28.320 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:55:28.321 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.321 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.321 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 21:55:28.326 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 21:55:28.326 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 21:55:28.327 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.327 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.327 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 21:55:28.332 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 21:55:28.332 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 21:55:28.333 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.333 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.333 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 21:55:28.339 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 21:55:28.340 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.340 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.340 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 21:55:28.346 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 21:55:28.346 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 21:55:28.347 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.347 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.347 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 21:55:28.353 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 21:55:28.353 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_ANALYZE` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 21:55:28.354 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.354 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.355 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE': maxForks=0; fair=false; array=0
Jul-09 21:55:28.358 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 21:55:28.358 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 21:55:28.359 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.359 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.359 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-09 21:55:28.364 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 21:55:28.364 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:TRACK_CLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 21:55:28.365 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.365 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.365 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES': maxForks=0; fair=false; array=0
Jul-09 21:55:28.373 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 21:55:28.374 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.374 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.374 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 21:55:28.378 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 21:55:28.378 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 21:55:28.379 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.379 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.379 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 21:55:28.383 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 21:55:28.384 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.384 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.384 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 21:55:28.389 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 21:55:28.390 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:55:28.390 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:55:28.390 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 21:55:28.394 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, TRACK_CLONES, PARSE_HLA_TYPES, OPTITYPE, MUTECT2, MERGE_VARIANTS, NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE, SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:55:28.401 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 21:55:28.402 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 21:55:28.403 [main] DEBUG nextflow.Session - Igniting dataflow network (34)
Jul-09 21:55:28.405 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 21:55:28.405 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 21:55:28.405 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 21:55:28.405 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 21:55:28.405 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 21:55:28.405 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 21:55:28.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 21:55:28.407 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_25891cc150c58c37: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_e31ace2ae3a0577f: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_353ca0e142176772: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_20ae2808df92087c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
  Script_d7ded221c09332db: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
  Script_c91e1620e2f6f3ad: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
Jul-09 21:55:28.407 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 21:55:28.407 [main] DEBUG nextflow.Session - Session await
Jul-09 21:55:28.414 [Actor Thread 14] ERROR nextflow.extension.DataflowHelper - @unknown
groovy.lang.MissingMethodException: No signature of method: Script_25891cc150c58c37$_runScript_closure1$_closure3$_closure4$_closure7.doCall() is applicable for argument types: (LinkedHashMap) values: [[id:SAMPLE_01_T, patient_id:PATIENT_01, timepoint:baseline, sample_type:tumor, hla_alleles:HLA-A*02:01;HLA-B*07:02, ...]]
Possible solutions: doCall(java.lang.Object, java.lang.Object), findAll(), findAll(), isCase(java.lang.Object), isCase(java.lang.Object)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:272)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at groovy.lang.Closure.call(Closure.java:433)
	at groovy.lang.Closure.call(Closure.java:422)
	at nextflow.extension.BranchOp.doNext(BranchOp.groovy:54)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at groovy.lang.MetaClassImpl.doInvokeMethod(MetaClassImpl.java:1333)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1088)
	at groovy.lang.MetaClassImpl.invokeMethodClosure(MetaClassImpl.java:1017)
	at groovy.lang.MetaClassImpl.doInvokeMethod(MetaClassImpl.java:1207)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1088)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at groovy.lang.Closure.call(Closure.java:433)
	at groovy.lang.Closure.call(Closure.java:422)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.extension.DataflowHelper$_subscribeImpl_closure2.doCall(DataflowHelper.groovy:287)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:280)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at groovy.lang.Closure.call(Closure.java:433)
	at groovyx.gpars.dataflow.operator.DataflowOperatorActor.startTask(DataflowOperatorActor.java:120)
	at groovyx.gpars.dataflow.operator.DataflowOperatorActor.onMessage(DataflowOperatorActor.java:108)
	at groovyx.gpars.actor.impl.SDAClosure$1.call(SDAClosure.java:43)
	at groovyx.gpars.actor.AbstractLoopingActor.runEnhancedWithoutRepliesOnMessages(AbstractLoopingActor.java:293)
	at groovyx.gpars.actor.AbstractLoopingActor.access$400(AbstractLoopingActor.java:30)
	at groovyx.gpars.actor.AbstractLoopingActor$1.handleMessage(AbstractLoopingActor.java:93)
	at groovyx.gpars.util.AsyncMessagingCore.run(AsyncMessagingCore.java:132)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Jul-09 21:55:28.420 [Actor Thread 14] DEBUG nextflow.Session - Session aborted -- Cause: No signature of method: Script_25891cc150c58c37$_runScript_closure1$_closure3$_closure4$_closure7.doCall() is applicable for argument types: (LinkedHashMap) values: [[id:SAMPLE_01_T, patient_id:PATIENT_01, timepoint:baseline, sample_type:tumor, hla_alleles:HLA-A*02:01;HLA-B*07:02, ...]]
Possible solutions: doCall(java.lang.Object, java.lang.Object), findAll(), findAll(), isCase(java.lang.Object), isCase(java.lang.Object)
Jul-09 21:55:28.431 [Actor Thread 14] DEBUG nextflow.Session - The following nodes are still active:
[process] IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) bound ; channel: fasta
  port 2: (value) bound ; channel: fai
  port 3: (value) bound ; channel: dict
  port 4: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) bound ; channel: fasta
  port 2: (value) bound ; channel: fai
  port 3: (value) bound ; channel: dict
  port 4: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
  status=ACTIVE
  port 0: (queue) closed; channel: fasta
  port 1: (queue) OPEN  ; channel: gtf
  port 2: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) OPEN  ; channel: index
  port 2: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jul-09 21:55:28.433 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 21:55:28.433 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 21:55:28.433 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 21:55:28.434 [Actor Thread 16] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 21:55:28.435 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=0ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=0; peakCpus=0; peakMemory=0; ]
Jul-09 21:55:28.435 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 21:55:28.436 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 21:55:28.804 [Actor Thread 18] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 21:55:28.927 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 21:55:29.004 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 21:55:29.006 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 21:55:29.016 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
