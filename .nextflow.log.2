Jul-09 23:39:57.121 [main] DEBUG nextflow.cli.Launcher - $> nextflow run run_tcr_longitudinal.nf -profile docker,test_tcr_longitudinal
Jul-09 23:39:57.150 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 23:39:57.160 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 23:39:57.174 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 23:39:57.174 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 23:39:57.175 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 23:39:57.179 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 23:39:57.186 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 23:39:57.187 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 23:39:57.203 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 23:39:57.204 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@53093491] - activable => nextflow.secret.LocalSecretsProvider@53093491
Jul-09 23:39:57.206 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `docker,test_tcr_longitudinal`
Jul-09 23:39:57.597 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, test_tcr_longitudinal, singularity, aws, docker, podman]
Jul-09 23:39:57.611 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 23:39:57.618 [main] DEBUG nextflow.cli.CmdRun - Launching `run_tcr_longitudinal.nf` [trusting_leavitt] DSL2 - revision: 9edc7a2341
Jul-09 23:39:57.618 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 23:39:57.618 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 23:39:57.638 [main] DEBUG nextflow.Session - Session UUID: e8065b04-acb4-4ca2-96bc-7a6e4bc4f2aa
Jul-09 23:39:57.639 [main] DEBUG nextflow.Session - Run name: trusting_leavitt
Jul-09 23:39:57.639 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 23:39:57.641 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 23:39:57.643 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 23:39:57.653 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (87.2 MB) - Swap: 5 GB (1.2 GB)
Jul-09 23:39:57.659 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 23:39:57.671 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 23:39:57.674 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 23:39:57.682 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 23:39:57.691 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 23:39:57.694 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 23:39:57.717 [main] DEBUG nextflow.Session - Session start
Jul-09 23:39:57.718 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results_tcr_longitudinal/pipeline_info/execution_trace_2025-07-09_23-39-57.txt
Jul-09 23:39:57.721 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 23:39:57.722 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 23:39:57.795 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 23:39:57.798 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_f14b54f2c3f45188: /Users/<USER>/Downloads/immune_neoantigen_pipeline/run_tcr_longitudinal.nf
Jul-09 23:39:57.799 [main] DEBUG nextflow.Session - Session aborted -- Cause: No such property: WorkflowMain for class: Script_f14b54f2c3f45188
Jul-09 23:39:57.803 [main] ERROR nextflow.cli.Launcher - @unknown
groovy.lang.MissingPropertyException: No such property: WorkflowMain for class: Script_f14b54f2c3f45188
	at org.codehaus.groovy.runtime.ScriptBytecodeAdapter.unwrap(ScriptBytecodeAdapter.java:67)
	at org.codehaus.groovy.vmplugin.v8.IndyGuardsFiltersAndSignatures.unwrap(IndyGuardsFiltersAndSignatures.java:163)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at Script_f14b54f2c3f45188.runScript(Script_f14b54f2c3f45188:25)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.script.BaseScript.run0(BaseScript.groovy:159)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.script.BaseScript.run(BaseScript.groovy:193)
	at nextflow.script.parser.v1.ScriptLoaderV1.runScript(ScriptLoaderV1.groovy:246)
	at nextflow.script.parser.v1.ScriptLoaderV1.runScript(ScriptLoaderV1.groovy)
	at nextflow.script.ScriptRunner.run(ScriptRunner.groovy:246)
	at nextflow.script.ScriptRunner.execute(ScriptRunner.groovy:139)
	at nextflow.cli.CmdRun.run(CmdRun.groovy:379)
	at nextflow.cli.Launcher.run(Launcher.groovy:513)
	at nextflow.cli.Launcher.main(Launcher.groovy:673)
