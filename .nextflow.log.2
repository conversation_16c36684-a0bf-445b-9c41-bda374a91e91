Jul-09 21:52:36.078 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 21:52:36.110 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 21:52:36.121 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 21:52:36.136 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 21:52:36.136 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 21:52:36.137 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 21:52:36.141 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 21:52:36.148 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 21:52:36.149 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 21:52:36.165 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 21:52:36.166 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@57b9e423] - activable => nextflow.secret.LocalSecretsProvider@57b9e423
Jul-09 21:52:36.168 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 21:52:36.586 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, singularity, aws, docker, podman]
Jul-09 21:52:36.597 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 21:52:36.604 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [suspicious_marconi] DSL2 - revision: 828a8f7898
Jul-09 21:52:36.604 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 21:52:36.604 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 21:52:36.626 [main] DEBUG nextflow.Session - Session UUID: 26c3f3f2-6c72-4be1-8271-d6263803cd0a
Jul-09 21:52:36.626 [main] DEBUG nextflow.Session - Run name: suspicious_marconi
Jul-09 21:52:36.626 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 21:52:36.629 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 21:52:36.631 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 21:52:36.643 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (71.8 MB) - Swap: 5 GB (1.2 GB)
Jul-09 21:52:36.648 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 21:52:36.660 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 21:52:36.663 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 21:52:36.670 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 21:52:36.679 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 21:52:36.683 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 21:52:36.705 [main] DEBUG nextflow.Session - Session start
Jul-09 21:52:36.706 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results/pipeline_info/execution_trace_2025-07-09_21-52-36.txt
Jul-09 21:52:36.709 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 21:52:36.710 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 21:52:36.856 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 21:52:37.406 [main] INFO  nextflow.Nextflow - Pipeline: immune_neoantigen_pipeline v1.0.0
Jul-09 21:52:37.466 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 21:52:37.469 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 21:52:37.474 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.474 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.476 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 21:52:37.479 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 21:52:37.480 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 21:52:37.487 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 21:52:37.519 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 21:52:37.519 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MUTECT2` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 21:52:37.521 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.521 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.521 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2': maxForks=0; fair=false; array=0
Jul-09 21:52:37.532 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 21:52:37.534 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.534 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.534 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS': maxForks=0; fair=false; array=0
Jul-09 21:52:37.538 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 21:52:37.539 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:OPTITYPE` matches process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 21:52:37.540 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.540 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.540 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE': maxForks=0; fair=false; array=0
Jul-09 21:52:37.547 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 21:52:37.548 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.548 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.548 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS': maxForks=0; fair=false; array=0
Jul-09 21:52:37.554 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:52:37.555 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:52:37.556 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.556 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.556 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 21:52:37.561 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 21:52:37.561 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_INDEX` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 21:52:37.562 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.562 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.562 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX': maxForks=0; fair=false; array=0
Jul-09 21:52:37.566 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 21:52:37.567 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:SALMON_QUANT` matches process IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 21:52:37.568 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.568 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.568 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT': maxForks=0; fair=false; array=0
Jul-09 21:52:37.574 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 21:52:37.574 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.575 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.575 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS': maxForks=0; fair=false; array=0
Jul-09 21:52:37.580 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 21:52:37.580 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:FASTQC` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 21:52:37.581 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.581 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.581 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC': maxForks=0; fair=false; array=0
Jul-09 21:52:37.588 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 21:52:37.589 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_ANALYZE` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 21:52:37.590 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.590 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.590 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE': maxForks=0; fair=false; array=0
Jul-09 21:52:37.594 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 21:52:37.594 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 21:52:37.595 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.595 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.595 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-09 21:52:37.601 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 21:52:37.601 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:TRACK_CLONES` matches process IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 21:52:37.602 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.602 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.602 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES': maxForks=0; fair=false; array=0
Jul-09 21:52:37.611 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 21:52:37.612 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.612 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.612 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES': maxForks=0; fair=false; array=0
Jul-09 21:52:37.616 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 21:52:37.616 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:NETMHCPAN` matches process IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 21:52:37.617 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.617 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.618 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN': maxForks=0; fair=false; array=0
Jul-09 21:52:37.622 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 21:52:37.623 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.623 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.623 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 21:52:37.629 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 21:52:37.630 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 21:52:37.630 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 21:52:37.630 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS': maxForks=0; fair=false; array=0
Jul-09 21:52:37.635 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: FASTQC, FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC, GENERATE_PEPTIDES, PRIORITIZE_NEOANTIGENS, MULTIQC, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC, MIXCR_EXPORTCLONES, SALMON_INDEX, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX, FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2, TRACK_CLONES, PARSE_HLA_TYPES, OPTITYPE, NETMHCPAN, MUTECT2, MERGE_VARIANTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES, MERGE_TRANSCRIPTS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS, MIXCR_ANALYZE, IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES, CUSTOM_DUMPSOFTWAREVERSIONS, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE, SALMON_QUANT, IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:52:37.643 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 21:52:37.644 [main] WARN  nextflow.Session - There's no process matching config selector: FILTERmutectcalls
Jul-09 21:52:37.644 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 21:52:37.645 [main] DEBUG nextflow.Session - Igniting dataflow network (34)
Jul-09 21:52:37.647 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN
Jul-09 21:52:37.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS
Jul-09 21:52:37.649 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS
Jul-09 21:52:37.649 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_a2a8ecf553678ff4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_workflow.nf
  Script_e31ace2ae3a0577f: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/wes_workflow.nf
  Script_1577b2e988ae0229: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/rna_quantification.nf
  Script_70801d64beaf0862: /Users/<USER>/Downloads/immune_neoantigen_pipeline/main.nf
  Script_353ca0e142176772: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/hla_typing.nf
  Script_20ae2808df92087c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/variant_calling.nf
  Script_646b27f5d5cabc81: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/neoantigen_workflow.nf
  Script_be455ce87231f82c: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/qc.nf
  Script_6fa14707f57eb8ca: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/rnaseq_workflow.nf
  Script_c91e1620e2f6f3ad: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/neoantigen_prediction.nf
  Script_d7ded221c09332db: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-09 21:52:37.649 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 21:52:37.649 [main] DEBUG nextflow.Session - Session await
Jul-09 21:52:38.265 [Actor Thread 6] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:38.882 [Actor Thread 5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:39.244 [Actor Thread 11] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 21:52:39.458 [Actor Thread 9] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:39.581 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:39.746 [Actor Thread 7] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:39.858 [Actor Thread 1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:39.961 [Actor Thread 16] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:40.085 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:40.275 [Actor Thread 15] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:40.356 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:40.758 [Actor Thread 6] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:41.356 [Actor Thread 5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:41.627 [Actor Thread 11] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 21:52:41.846 [Actor Thread 9] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:41.957 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.073 [Actor Thread 7] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.181 [Actor Thread 1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.330 [Actor Thread 16] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.442 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.574 [Actor Thread 15] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.689 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:42.963 [Actor Thread 6] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:43.201 [Actor Thread 5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:43.239 [FileTransfer-1] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-26c3f3f2-6c72-4be1-8271-d6263803cd0a/b3/f752072ea26fbb66b84d6e0ce97764/genome.fasta
Jul-09 21:52:43.273 [Actor Thread 9] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:43.411 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 21:52:43.429 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:43.494 [FileTransfer-1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 21:52:43.501 [FileTransfer-2] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-26c3f3f2-6c72-4be1-8271-d6263803cd0a/d6/870a6474be3db47199f3de7794c315/test_1.fastq.gz
Jul-09 21:52:43.502 [FileTransfer-3] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-26c3f3f2-6c72-4be1-8271-d6263803cd0a/a2/4ea2a4197c37cb54a07bd8e0bf8eb6/test_2.fastq.gz
Jul-09 21:52:43.529 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:43.533 [Actor Thread 1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:43.609 [Actor Thread 16] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:43.610 [FileTransfer-3] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:43.643 [FileTransfer-2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:43.685 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:43.730 [FileTransfer-3] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:43.916 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:44.163 [Actor Thread 6] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:44.428 [Actor Thread 5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:44.480 [FileTransfer-4] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-26c3f3f2-6c72-4be1-8271-d6263803cd0a/6f/6000a91960e1d6b4e43aead71bcc97/genome.gtf
Jul-09 21:52:44.514 [Actor Thread 9] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:44.514 [FileTransfer-4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 21:52:44.592 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:44.592 [FileTransfer-4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.gtf
Jul-09 21:52:44.689 [Task submitter] WARN  nextflow.container.DockerBuilder - Undocumented setting `docker.userEmulation` is not supported any more - please remove it from your config
Jul-09 21:52:44.704 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 21:52:44.705 [Task submitter] INFO  nextflow.Session - [8f/cd7e35] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta)
Jul-09 21:52:44.734 [Actor Thread 1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:44.766 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 21:52:44.766 [Task submitter] INFO  nextflow.Session - [44/75428b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N)
Jul-09 21:52:44.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jul-09 21:52:44.769 [Task submitter] INFO  nextflow.Session - [c7/22475b] Submitted process > IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)
Jul-09 21:52:44.781 [Actor Thread 16] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:44.794 [Actor Thread 16] DEBUG nextflow.processor.TaskProcessor - Process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC > collision check staging file names: [test_2.fastq.gz:2, test_1.fastq.gz:2]
Jul-09 21:52:44.796 [Actor Thread 16] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (3); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz
Jul-09 21:52:44.802 [Actor Thread 16] ERROR nextflow.processor.TaskProcessor - Error executing process > 'IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (3)'

Caused by:
  Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz



Container:
  quay.io/biocontainers/fastqc:0.11.9--0

Tip: view the complete command output by changing to the process work dir and entering the command `cat .command.out`
Jul-09 21:52:44.803 [Actor Thread 16] INFO  nextflow.Session - Execution cancelled -- Finishing pending tasks before exit
Jul-09 21:52:44.805 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 21:52:44.805 [Actor Thread 18] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 21:52:44.805 [Actor Thread 3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 21:52:44.805 [Actor Thread 21] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jul-09 21:52:44.833 [Actor Thread 2] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:45.006 [Actor Thread 2] DEBUG nextflow.processor.TaskProcessor - Process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE > collision check staging file names: [test_2.fastq.gz:2, test_1.fastq.gz:2]
Jul-09 21:52:45.006 [Actor Thread 2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (3); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz
Jul-09 21:52:45.042 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:45.084 [FileTransfer-5] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-26c3f3f2-6c72-4be1-8271-d6263803cd0a/fd/961f926487c412296d922063a6ba99/test_rnaseq_1.fastq.gz
Jul-09 21:52:45.084 [FileTransfer-6] DEBUG nextflow.file.FilePorter - Copying foreign file https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz to work dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/stage-26c3f3f2-6c72-4be1-8271-d6263803cd0a/dd/a5c271a847e84e0d1bad21bd5508e7/test_rnaseq_2.fastq.gz
Jul-09 21:52:45.113 [FileTransfer-6] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:45.117 [Actor Thread 9] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:45.120 [FileTransfer-5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:45.199 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:45.206 [FileTransfer-6] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_2.fastq.gz
Jul-09 21:52:45.210 [FileTransfer-5] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_rnaseq_1.fastq.gz
Jul-09 21:52:45.269 [Actor Thread 1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:45.365 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:45.512 [Actor Thread 9] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:45.524 [Actor Thread 9] DEBUG nextflow.processor.TaskProcessor - Process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE > collision check staging file names: [test_2.fastq.gz:2, test_rnaseq_2.fastq.gz:1, test_1.fastq.gz:2, test_rnaseq_1.fastq.gz:1]
Jul-09 21:52:45.524 [Actor Thread 9] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (1); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz
Jul-09 21:52:45.560 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:45.637 [Actor Thread 1] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:45.716 [Actor Thread 1] DEBUG nextflow.processor.TaskProcessor - Process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC > collision check staging file names: [test_2.fastq.gz:2, test_rnaseq_2.fastq.gz:1, test_1.fastq.gz:2, test_rnaseq_1.fastq.gz:1]
Jul-09 21:52:45.716 [Actor Thread 1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (1); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz
Jul-09 21:52:45.745 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:45.813 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 21:52:45.885 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_1.fastq.gz
Jul-09 21:52:46.221 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 21:52:46.526 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/illumina/fastq/test_2.fastq.gz
Jul-09 21:52:46.781 [Actor Thread 8] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 21:52:47.056 [Actor Thread 8] DEBUG nextflow.processor.TaskProcessor - Process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 > collision check staging file names: [genome.fasta:1, genome.dict:1, test_2.fastq.gz:3, test_1.fastq.gz:3, genome.fasta.fai:1]
Jul-09 21:52:47.056 [Actor Thread 8] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (2); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz
Jul-09 21:52:47.092 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta
Jul-09 21:52:47.170 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.fasta.fai
Jul-09 21:52:47.245 [Actor Thread 4] DEBUG n.file.http.XFileSystemProvider - Remote redirect location: https://raw.githubusercontent.com/nf-core/test-datasets/modules/data/genomics/homo_sapiens/genome/genome.dict
Jul-09 21:52:47.289 [Actor Thread 4] DEBUG nextflow.processor.TaskProcessor - Process IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 > collision check staging file names: [genome.fasta:1, genome.dict:1, test_2.fastq.gz:3, test_rnaseq_2.fastq.gz:1, test_1.fastq.gz:3, test_rnaseq_1.fastq.gz:1, genome.fasta.fai:1]
Jul-09 21:52:47.289 [Actor Thread 4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2 (1); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2` input file name collision -- There are multiple input files for each of the following file names: test_2.fastq.gz, test_1.fastq.gz
Jul-09 21:52:50.342 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/c7/22475bb9c3645e0131947ae30c5209]
Jul-09 21:52:50.342 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 21:52:50.346 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N); work-dir=/Users/<USER>/Downloads/immune_neoantigen_pipeline/work/c7/22475bb9c3645e0131947ae30c5209
  error [nextflow.exception.ProcessFailedException]: Process `IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE (SAMPLE_01_N)` terminated with an error exit status (125)
Jul-09 21:53:04.064 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX (genome.fasta); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/8f/cd7e35f3ab85d947f1c6bda2006a78]
Jul-09 21:53:04.073 [TaskFinalizer-2] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 21:53:13.469 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC (SAMPLE_01_N); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/44/75428b8ad426fffa8cb75d63b60523]
Jul-09 21:53:13.469 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 21:53:13.470 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 21:53:13.481 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jul-09 21:53:13.481 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jul-09 21:53:13.485 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=2; failedCount=1; ignoredCount=0; cachedCount=0; pendingCount=2; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=18s; failedDuration=11.1s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=18 GB; ]
Jul-09 21:53:13.485 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 21:53:13.485 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 21:53:13.968 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 21:53:14.043 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 21:53:14.163 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 21:53:14.174 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jul-09 21:53:14.175 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
