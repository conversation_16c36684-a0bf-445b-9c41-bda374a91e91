Jul-09 23:40:23.176 [main] DEBUG nextflow.cli.Launcher - $> nextflow run run_tcr_longitudinal.nf -profile docker,test_tcr_longitudinal
Jul-09 23:40:23.207 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 23:40:23.217 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 23:40:23.232 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 23:40:23.232 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 23:40:23.233 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 23:40:23.237 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 23:40:23.244 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 23:40:23.245 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 23:40:23.262 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 23:40:23.263 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@57b9e423] - activable => nextflow.secret.LocalSecretsProvider@57b9e423
Jul-09 23:40:23.265 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `docker,test_tcr_longitudinal`
Jul-09 23:40:23.676 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [slurm, debug, shifter, test, mamba, charliecloud, conda, test_tcr_longitudinal, singularity, aws, docker, podman]
Jul-09 23:40:23.689 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 23:40:23.695 [main] DEBUG nextflow.cli.CmdRun - Launching `run_tcr_longitudinal.nf` [irreverent_kare] DSL2 - revision: 8168ec70cf
Jul-09 23:40:23.695 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 23:40:23.696 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 23:40:23.715 [main] DEBUG nextflow.Session - Session UUID: 8558a992-9c78-4e6b-a42f-8273accd400b
Jul-09 23:40:23.715 [main] DEBUG nextflow.Session - Run name: irreverent_kare
Jul-09 23:40:23.715 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 23:40:23.718 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 23:40:23.719 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 23:40:23.730 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (68.5 MB) - Swap: 5 GB (1.2 GB)
Jul-09 23:40:23.735 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 23:40:23.746 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 23:40:23.749 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 23:40:23.757 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 23:40:23.765 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 23:40:23.768 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 23:40:23.791 [main] DEBUG nextflow.Session - Session start
Jul-09 23:40:23.792 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/results_tcr_longitudinal/pipeline_info/execution_trace_2025-07-09_23-40-23.txt
Jul-09 23:40:23.795 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 23:40:23.796 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 23:40:23.867 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 23:40:23.871 [main] INFO  nextflow.Nextflow - TCR LONGITUDINAL ANALYSIS PIPELINE
===================================
input    : assets/samplesheet_tcr_longitudinal.csv
outdir   : results_tcr_longitudinal

Jul-09 23:40:24.128 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
Jul-09 23:40:24.134 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:40:24.134 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:40:24.137 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jul-09 23:40:24.139 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=16; memory=48 GB; capacity=16; pollInterval=100ms; dumpInterval=5m
Jul-09 23:40:24.140 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jul-09 23:40:24.147 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN': maxForks=0; fair=false; array=0
Jul-09 23:40:24.160 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
Jul-09 23:40:24.162 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:40:24.162 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:40:24.162 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE': maxForks=0; fair=false; array=0
Jul-09 23:40:24.166 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
Jul-09 23:40:24.167 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:40:24.167 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:40:24.167 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER': maxForks=0; fair=false; array=0
Jul-09 23:40:24.171 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-09 23:40:24.171 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MIXCR_EXPORTCLONES` matches process NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-09 23:40:24.172 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:40:24.172 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:40:24.172 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES': maxForks=0; fair=false; array=0
Jul-09 23:40:24.180 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
Jul-09 23:40:24.181 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jul-09 23:40:24.182 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jul-09 23:40:24.182 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS': maxForks=0; fair=false; array=0
Jul-09 23:40:24.185 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN, MIXCR_ALIGN, MIXCR_FILTER, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER, MIXCR_ASSEMBLE, IMMUNARCH_ANALYSIS, NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS, MIXCR_EXPORTCLONES
Jul-09 23:40:24.194 [main] WARN  nextflow.Session - There's no process matching config selector: FASTQC
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: MULTIQC
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: MUTECT2
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: SALMON_QUANT
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: MIXCR_ANALYZE -- Did you mean: MIXCR_ALIGN?
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: NETMHCPAN
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: OPTITYPE
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: SAMPLESHEET_CHECK
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: FILTERMUTECTCALLS
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: SALMON_INDEX
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: NEOANTIGEN_FILTER
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: CUSTOM_DUMPSOFTWAREVERSIONS
Jul-09 23:40:24.195 [main] WARN  nextflow.Session - There's no process matching config selector: TRACK_CLONES
Jul-09 23:40:24.196 [main] DEBUG nextflow.Session - Igniting dataflow network (8)
Jul-09 23:40:24.198 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
Jul-09 23:40:24.198 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
Jul-09 23:40:24.198 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
Jul-09 23:40:24.198 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
Jul-09 23:40:24.198 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
Jul-09 23:40:24.198 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_14378b34c085d191: /Users/<USER>/Downloads/immune_neoantigen_pipeline/workflows/tcr_longitudinal.nf
  Script_2353f87ed01e71a4: /Users/<USER>/Downloads/immune_neoantigen_pipeline/run_tcr_longitudinal.nf
  Script_6fc2ee910f352d3e: /Users/<USER>/Downloads/immune_neoantigen_pipeline/modules/tcr_analysis.nf
Jul-09 23:40:24.198 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jul-09 23:40:24.198 [main] DEBUG nextflow.Session - Session await
Jul-09 23:40:24.201 [Actor Thread 15] ERROR nextflow.extension.OperatorImpl - @unknown
java.lang.IllegalArgumentException: Argument of `file` function cannot be null
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.Nextflow.file(Nextflow.groovy:121)
	at nextflow.Nextflow.file(Nextflow.groovy)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at Script_2353f87ed01e71a4.create_tcr_channels_from_samplesheet(Script_2353f87ed01e71a4:97)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at nextflow.script.FunctionDef.invoke_a(FunctionDef.groovy:64)
	at nextflow.script.ComponentDef.invoke_o(ComponentDef.groovy:40)
	at nextflow.script.WorkflowBinding.invokeMethod(WorkflowBinding.groovy:105)
	at org.codehaus.groovy.runtime.InvokerHelper.invokePogoMethod(InvokerHelper.java:651)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethod(InvokerHelper.java:628)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeOnDelegationObjects(ClosureMetaClass.java:392)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:331)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at org.codehaus.groovy.runtime.InvokerHelper.invokePogoMethod(InvokerHelper.java:645)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethod(InvokerHelper.java:628)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeOnDelegationObjects(ClosureMetaClass.java:392)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:329)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at Script_2353f87ed01e71a4$_runScript_closure1$_closure3$_closure4.doCall(Script_2353f87ed01e71a4:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:280)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.extension.MapOp$_apply_closure1.doCall(MapOp.groovy:56)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:280)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at groovy.lang.Closure.call(Closure.java:433)
	at groovyx.gpars.dataflow.operator.DataflowOperatorActor.startTask(DataflowOperatorActor.java:120)
	at groovyx.gpars.dataflow.operator.DataflowOperatorActor.onMessage(DataflowOperatorActor.java:108)
	at groovyx.gpars.actor.impl.SDAClosure$1.call(SDAClosure.java:43)
	at groovyx.gpars.actor.AbstractLoopingActor.runEnhancedWithoutRepliesOnMessages(AbstractLoopingActor.java:293)
	at groovyx.gpars.actor.AbstractLoopingActor.access$400(AbstractLoopingActor.java:30)
	at groovyx.gpars.actor.AbstractLoopingActor$1.handleMessage(AbstractLoopingActor.java:93)
	at groovyx.gpars.util.AsyncMessagingCore.run(AsyncMessagingCore.java:132)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Jul-09 23:40:24.204 [Actor Thread 15] DEBUG nextflow.Session - Session aborted -- Cause: Argument of `file` function cannot be null
Jul-09 23:40:24.211 [Actor Thread 15] DEBUG nextflow.Session - The following nodes are still active:
[process] NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jul-09 23:40:24.212 [main] DEBUG nextflow.Session - Session await > all processes finished
Jul-09 23:40:24.213 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jul-09 23:40:24.213 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jul-09 23:40:24.214 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=0ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=0; peakCpus=0; peakMemory=0; ]
Jul-09 23:40:24.215 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jul-09 23:40:24.215 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jul-09 23:40:24.682 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jul-09 23:40:24.751 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jul-09 23:40:24.753 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jul-09 23:40:24.765 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
