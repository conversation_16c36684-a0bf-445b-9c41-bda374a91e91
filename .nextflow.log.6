Jul-09 21:49:03.513 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 21:49:03.546 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 21:49:03.557 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 21:49:03.574 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 21:49:03.574 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 21:49:03.576 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 21:49:03.581 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 21:49:03.589 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 21:49:03.590 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
Jul-09 21:49:03.605 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 21:49:03.607 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@53093491] - activable => nextflow.secret.LocalSecretsProvider@53093491
Jul-09 21:49:03.610 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jul-09 21:49:04.020 [main] DEBUG nextflow.config.ConfigBuilder - In the following config snippet the attribute `params.publish_dir_mode` is empty:
  input='assets/samplesheet_test.csv'
  outdir='./results'
  run_wes=true
  run_rnaseq=true
  run_tcr=true
  run_neoantigen=true
  genome='GRCh38'
  fasta='https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta'
  gtf='https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf'
  mutect2_extra_args=''
  min_base_quality_score=20
  salmon_index=null
  salmon_extra_args=''
  mixcr_species='hsa'
  mixcr_extra_args=''
  netmhcpan_version='4.1'
  peptide_lengths='9,10'
  binding_threshold=1000
  optitype_extra_args=''
  skip_qc=false
  skip_multiqc=false
  max_memory='6.GB'
  max_cpus=2
  max_time='6.h'
  singularity_pull_docker_container=false
  custom_config_version='master'
  custom_config_base="https://raw.githubusercontent.com/nf-core/configs/master"
  config_profile_description='Minimal test dataset to check pipeline function'
  config_profile_contact=null
  config_profile_url=null
  config_profile_name='Test profile'
  validationFailUnrecognisedParams=false
  validationLenientMode=false
  validationSchemaIgnoreParams='genomes'
  validationShowHiddenParams=false
  validate_params=true
  help=false
  version=false

Jul-09 21:49:04.021 [main] ERROR nextflow.cli.Launcher - Unknown config attribute `params.publish_dir_mode` -- check config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
nextflow.exception.ConfigParseException: Unknown config attribute `params.publish_dir_mode` -- check config file: /Users/<USER>/Downloads/immune_neoantigen_pipeline/nextflow.config
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.validate(ConfigBuilder.groovy:462)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.validate(ConfigBuilder.groovy:469)
	at nextflow.config.ConfigBuilder.validate(ConfigBuilder.groovy:449)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.merge0(ConfigBuilder.groovy:419)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.buildConfig0(ConfigBuilder.groovy:374)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.buildGivenFiles(ConfigBuilder.groovy:318)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.buildConfigObject(ConfigBuilder.groovy:816)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.config.ConfigBuilder.build(ConfigBuilder.groovy:829)
	at nextflow.cli.CmdRun.run(CmdRun.groovy:333)
	at nextflow.cli.Launcher.run(Launcher.groovy:513)
	at nextflow.cli.Launcher.main(Launcher.groovy:673)
