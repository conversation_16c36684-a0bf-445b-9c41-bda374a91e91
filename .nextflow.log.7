Jul-09 21:48:29.757 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf -profile test,docker
Jul-09 21:48:29.783 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.6
Jul-09 21:48:29.794 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.1,nf-k8s@1.0.0,nf-tower@1.11.4,nf-wave@1.12.1
Jul-09 21:48:29.809 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jul-09 21:48:29.809 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jul-09 21:48:29.810 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jul-09 21:48:29.814 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jul-09 21:48:29.840 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jul-09 21:48:29.846 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [festering_shockley] DSL2 - revision: 4db273a87e
Jul-09 21:48:29.847 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jul-09 21:48:29.847 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jul-09 21:48:29.868 [main] DEBUG nextflow.Session - Session UUID: ad9563a6-fb56-4ef2-8fcb-8046c81232c7
Jul-09 21:48:29.868 [main] DEBUG nextflow.Session - Run name: festering_shockley
Jul-09 21:48:29.868 [main] DEBUG nextflow.Session - Executor pool size: 16
Jul-09 21:48:29.871 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jul-09 21:48:29.873 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=48; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jul-09 21:48:29.884 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.6 build 5954
  Created: 01-07-2025 11:27 UTC (04:27 PDT)
  System: Mac OS X 15.5
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 21.0.6+9-b895.97
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 16 - Mem: 48 GB (228.2 MB) - Swap: 5 GB (1.2 GB)
Jul-09 21:48:29.891 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/Downloads/immune_neoantigen_pipeline/work [Mac OS X]
Jul-09 21:48:29.906 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jul-09 21:48:29.909 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jul-09 21:48:29.911 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jul-09 21:48:29.920 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jul-09 21:48:29.923 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 17; maxThreads: 1000
Jul-09 21:48:29.947 [main] DEBUG nextflow.Session - Session start
Jul-09 21:48:29.951 [main] DEBUG nextflow.Session - Using default localLib path: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 21:48:29.952 [main] DEBUG nextflow.Session - Adding to the classpath library: /Users/<USER>/Downloads/immune_neoantigen_pipeline/lib
Jul-09 21:48:30.264 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jul-09 21:48:30.268 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jul-09 21:48:30.271 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@153cb763] - activable => nextflow.secret.LocalSecretsProvider@153cb763
Jul-09 21:48:30.871 [main] WARN  nextflow.script.ScriptBinding - Access to undefined parameter `help` -- Initialise it to a default value eg. `params.help = some_value`
Jul-09 21:48:30.875 [main] INFO  nextflow.Nextflow - Pipeline: null vnull
Jul-09 21:48:30.875 [main] WARN  nextflow.script.ScriptBinding - Access to undefined parameter `input` -- Initialise it to a default value eg. `params.input = some_value`
Jul-09 21:48:30.876 [main] ERROR nextflow.Nextflow - ERROR: Please provide input samplesheet with --input
