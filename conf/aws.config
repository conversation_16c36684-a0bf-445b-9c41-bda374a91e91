/*
========================================================================================
    Nextflow config file for running on AWS Batch
========================================================================================
    Configuration for running the pipeline on AWS Batch with S3 storage.
========================================================================================
*/

params {
    config_profile_name        = 'AWS Batch profile'
    config_profile_description = 'Configuration for AWS Batch execution'
    
    // AWS-specific parameters
    max_memory                 = '500.GB'
    max_cpus                   = 64
    max_time                   = '72.h'
    
    // S3 bucket for intermediate files
    outdir                     = 's3://your-bucket/results'
}

process {
    executor = 'awsbatch'
    queue = 'your-batch-queue'
    
    // Use Docker containers from ECR
    container = 'your-account.dkr.ecr.region.amazonaws.com/immune_neoantigen:latest'
    
    // AWS Batch job definitions
    withLabel:process_single {
        cpus = 1
        memory = '4.GB'
        queue = 'small-queue'
    }
    
    withLabel:process_low {
        cpus = 2
        memory = '8.GB'
        queue = 'small-queue'
    }
    
    withLabel:process_medium {
        cpus = 8
        memory = '32.GB'
        queue = 'medium-queue'
    }
    
    withLabel:process_high {
        cpus = 16
        memory = '64.GB'
        queue = 'large-queue'
    }
    
    withLabel:process_long {
        cpus = 8
        memory = '32.GB'
        queue = 'long-queue'
    }
    
    withLabel:process_high_memory {
        cpus = 16
        memory = '128.GB'
        queue = 'highmem-queue'
    }
    
    // Process-specific AWS configurations
    withName:MUTECT2 {
        cpus = 8
        memory = '32.GB'
        queue = 'medium-queue'
    }
    
    withName:SALMON_QUANT {
        cpus = 16
        memory = '64.GB'
        queue = 'large-queue'
    }
    
    withName:MIXCR_ANALYZE {
        cpus = 16
        memory = '64.GB'
        queue = 'large-queue'
    }
    
    withName:NETMHCPAN {
        cpus = 8
        memory = '32.GB'
        queue = 'long-queue'
    }
    
    withName:OPTITYPE {
        cpus = 8
        memory = '32.GB'
        queue = 'medium-queue'
    }
}

aws {
    region = 'us-east-1'
    batch {
        cliPath = '/home/<USER>/miniconda/bin/aws'
        maxParallelTransfers = 4
        maxTransferAttempts = 3
        delayBetweenAttempts = '5 sec'
    }
}

// Work directory on S3
workDir = 's3://your-bucket/work'

// Enable CloudWatch logs
aws.batch.logsGroup = '/aws/batch/job'

// Executor settings
executor {
    $awsbatch {
        queueSize = 100
        submitRateLimit = '50 sec'
        pollInterval = '30 sec'
    }
}

// Enable trace and timeline for monitoring
trace {
    enabled = true
    file = "${params.outdir}/pipeline_info/execution_trace.txt"
    fields = 'task_id,hash,native_id,process,tag,name,status,exit,module,container,cpus,time,disk,memory,attempt,submit,start,complete,duration,realtime,queue,%cpu,%mem,rss,vmem,peak_rss,peak_vmem'
}

timeline {
    enabled = true
    file = "${params.outdir}/pipeline_info/execution_timeline.html"
}

report {
    enabled = true
    file = "${params.outdir}/pipeline_info/execution_report.html"
}
