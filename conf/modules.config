/*
========================================================================================
    Config file for defining DSL2 per module options and publishing paths
========================================================================================
    Available keys to override module options:
        ext.args   = Additional arguments appended to command in module.
        ext.args2  = Second set of arguments appended to command in module (multi-tool modules).
        ext.args3  = Third set of arguments appended to command in module (multi-tool modules).
        ext.prefix = File name prefix for output files.
========================================================================================
*/

process {

    publishDir = [
        path: { "${params.outdir}/${task.process.tokenize(':')[-1].tokenize('_')[0].toLowerCase()}" },
        mode: params.publish_dir_mode,
        saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
    ]

    withName: SAMPLESHEET_CHECK {
        publishDir = [
            path: { "${params.outdir}/pipeline_info" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }

    withName: FASTQC {
        ext.args = '--quiet'
        publishDir = [
            path: { "${params.outdir}/qc/fastqc" },
            mode: params.publish_dir_mode,
            pattern: "*.{html,zip}"
        ]
    }

    withName: MULTIQC {
        ext.args = ''
        publishDir = [
            path: { "${params.outdir}/multiqc" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }

    // WES workflow modules
    withName: MUTECT2 {
        ext.args = { params.mutect2_extra_args }
        publishDir = [
            path: { "${params.outdir}/variants/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{vcf.gz,vcf.gz.tbi,stats}"
        ]
    }

    withName: FILTERMUTECTCALLS {
        publishDir = [
            path: { "${params.outdir}/variants/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{vcf.gz,vcf.gz.tbi}"
        ]
    }

    withName: OPTITYPE {
        ext.args = ""
        publishDir = [
            path: { "${params.outdir}/hla/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{tsv,pdf}"
        ]
    }

    // RNA-seq workflow modules
    withName: SALMON_INDEX {
        publishDir = [
            path: { "${params.outdir}/genome/index" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }

    withName: SALMON_QUANT {
        ext.args = { params.salmon_extra_args }
        publishDir = [
            path: { "${params.outdir}/transcripts/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{sf,json}"
        ]
    }

    // TCR workflow modules
    withName: MIXCR_ANALYZE {
        ext.args = { "${params.mixcr_extra_args}" }
        publishDir = [
            path: { "${params.outdir}/tcr/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{txt,json,vdjca,clns}"
        ]
    }

    withName: MIXCR_EXPORTCLONES {
        publishDir = [
            path: { "${params.outdir}/tcr/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{txt,tsv}"
        ]
    }

    // Neoantigen workflow modules
    withName: NETMHCPAN {
        ext.args = { "-l ${params.peptide_lengths}" }
        publishDir = [
            path: { "${params.outdir}/neoantigens/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{txt,xls}"
        ]
    }

    withName: NEOANTIGEN_FILTER {
        ext.args = { "--binding_threshold ${params.binding_threshold}" }
        publishDir = [
            path: { "${params.outdir}/neoantigens/${meta.id}" },
            mode: params.publish_dir_mode,
            pattern: "*.{txt,tsv}"
        ]
    }

    // Utility modules
    withName: CUSTOM_DUMPSOFTWAREVERSIONS {
        publishDir = [
            path: { "${params.outdir}/pipeline_info" },
            mode: params.publish_dir_mode,
            pattern: '*_versions.yml'
        ]
    }

    withName: 'TRACK_CLONES' {
        publishDir = [
            path: { "${params.outdir}/reports/clonotype_tracking" },
            mode: params.publish_dir_mode,
            pattern: "*.{pdf,png,html}"
        ]
    }
}
