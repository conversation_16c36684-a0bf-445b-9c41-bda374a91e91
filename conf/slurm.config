/*
========================================================================================
    Nextflow config file for running on SLURM clusters
========================================================================================
    Configuration for running the pipeline on SLURM-based HPC systems.
    Assumes Singularity is available and configured.
========================================================================================
*/

params {
    config_profile_name        = 'SLURM cluster profile'
    config_profile_description = 'Configuration for SLURM-based HPC clusters'
    
    // Increase default resources for HPC
    max_memory                 = '500.GB'
    max_cpus                   = 64
    max_time                   = '72.h'
}

process {
    executor = 'slurm'
    
    // Default SLURM options
    clusterOptions = '--account=your_account --partition=your_partition'
    
    // Use Singularity containers
    container = 'docker://your_registry/immune_neoantigen:latest'
    
    // Queue-specific configurations
    withLabel:process_single {
        queue = 'short'
        time  = '2.h'
    }
    
    withLabel:process_low {
        queue = 'short'
        time  = '4.h'
    }
    
    withLabel:process_medium {
        queue = 'medium'
        time  = '12.h'
    }
    
    withLabel:process_high {
        queue = 'long'
        time  = '24.h'
    }
    
    withLabel:process_long {
        queue = 'long'
        time  = '48.h'
    }
    
    withLabel:process_high_memory {
        queue = 'highmem'
        memory = '500.GB'
        time   = '24.h'
    }
    
    // Process-specific SLURM configurations
    withName:MUTECT2 {
        queue = 'medium'
        cpus = 8
        memory = '32.GB'
        time = '12.h'
        clusterOptions = '--account=your_account --partition=medium --gres=gpu:0'
    }
    
    withName:SALMON_QUANT {
        queue = 'medium'
        cpus = 16
        memory = '64.GB'
        time = '8.h'
    }
    
    withName:MIXCR_ANALYZE {
        queue = 'medium'
        cpus = 16
        memory = '64.GB'
        time = '12.h'
    }
    
    withName:NETMHCPAN {
        queue = 'long'
        cpus = 8
        memory = '32.GB'
        time = '24.h'
    }
    
    withName:OPTITYPE {
        queue = 'medium'
        cpus = 8
        memory = '32.GB'
        time = '8.h'
    }
}

singularity {
    enabled = true
    autoMounts = true
    cacheDir = '/path/to/singularity/cache'
}

// Executor settings
executor {
    $slurm {
        queueSize = 50
        submitRateLimit = '10 sec'
        pollInterval = '30 sec'
    }
}

// Enable trace and timeline for monitoring
trace {
    enabled = true
    file = "${params.outdir}/pipeline_info/execution_trace.txt"
    fields = 'task_id,hash,native_id,process,tag,name,status,exit,module,container,cpus,time,disk,memory,attempt,submit,start,complete,duration,realtime,queue,%cpu,%mem,rss,vmem,peak_rss,peak_vmem,rchar,wchar,syscr,syscw,read_bytes,write_bytes'
}

timeline {
    enabled = true
    file = "${params.outdir}/pipeline_info/execution_timeline.html"
}

report {
    enabled = true
    file = "${params.outdir}/pipeline_info/execution_report.html"
}
