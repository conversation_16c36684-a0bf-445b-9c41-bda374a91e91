/*
========================================================================================
    Nextflow config file for running minimal tests
========================================================================================
    Defines input files and everything required to run a fast and simple pipeline test.

    Use as follows:
        nextflow run main.nf -profile test,docker

========================================================================================
*/

params {
    config_profile_name        = 'Test profile'
    config_profile_description = 'Minimal test dataset to check pipeline function'

    // Limit resources so that this can run on GitHub Actions
    max_cpus   = 2
    max_memory = '6.GB'
    max_time   = '6.h'

    // Skip problematic modules for testing
    skip_hla_typing = false
    skip_tcr_analysis = false
    run_tcr = true

    // Input data
    input  = 'assets/samplesheet_test.csv'

    // Genome references
    genome = 'GRCh38'
    fasta  = 'https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.fasta'
    gtf    = 'https://github.com/nf-core/test-datasets/raw/modules/data/genomics/homo_sapiens/genome/genome.gtf'

    // Workflow control - run all modules for testing
    run_wes        = true
    run_rnaseq     = true
    run_tcr        = true
    run_neoantigen = true

    // Reduced parameters for faster testing
    peptide_lengths    = '9,10'
    binding_threshold  = 1000

    // Skip time-consuming steps
    skip_qc      = false
    skip_multiqc = false
}
