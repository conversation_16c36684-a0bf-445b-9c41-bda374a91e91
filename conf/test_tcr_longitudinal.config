/*
========================================================================================
    Nextflow config file for running TCR longitudinal analysis tests
========================================================================================
*/

params {
    config_profile_name        = 'TCR Longitudinal Test profile'
    config_profile_description = 'Minimal test dataset for TCR longitudinal analysis validation'

    // Limit resources so that this can run on GitHub Actions
    max_cpus   = 2
    max_memory = '6.GB'
    max_time   = '6.h'

    // Input data - TCR longitudinal test data
    input  = 'assets/samplesheet_tcr_longitudinal.csv'

    // Genome references (minimal for testing)
    genome = 'GRCh38'
    igenomes_ignore = true

    // Skip modules not needed for TCR analysis
    skip_hla_typing = true
    skip_variant_calling = true
    skip_rnaseq = true
    
    // Enable TCR analysis
    run_tcr = true
    skip_tcr_analysis = false

    // MiXCR parameters
    mixcr_species = 'hsa'

    // Filtering parameters
    mixcr_min_clone_count = 2
    mixcr_min_clone_fraction = 0.00001

    // Collapsing parameters
    mixcr_collapse_by = 'CDR3'  // Options: CDR3, V, J, allVHitsWithScore, allJHitsWithScore
    mixcr_collapse_threshold = 0.9  // Similarity threshold (0.0-1.0)

    mixcr_extra_args = ''

    // Output directory
    outdir = 'results_tcr_longitudinal'

    // Disable publication of results
    publish_dir_mode = 'copy'

    // Enable detailed reporting
    tracedir = "${params.outdir}/pipeline_info"
}
