# Base Dockerfile for immune neoantigen pipeline
# Contains common dependencies for all pipeline modules

FROM ubuntu:20.04

LABEL maintainer="lilei <<EMAIL>>"
LABEL description="Base container for immune neoantigen pipeline"

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    git \
    build-essential \
    python3 \
    python3-pip \
    python3-dev \
    r-base \
    r-base-dev \
    openjdk-11-jdk \
    unzip \
    gzip \
    bzip2 \
    tabix \
    bcftools \
    samtools \
    && rm -rf /var/lib/apt/lists/*

# Set JAVA_HOME
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# Install Python packages
RUN pip3 install --no-cache-dir \
    pandas \
    numpy \
    scipy \
    biopython \
    pysam \
    pyvcf \
    matplotlib \
    seaborn

# Install R packages
RUN R -e "install.packages(c('dplyr', 'ggplot2', 'readr', 'tidyr', 'stringr', 'RColorBrewer'), repos='https://cran.rstudio.com/')"

# Create working directory
WORKDIR /opt/pipeline

# Copy pipeline scripts
COPY bin/ /opt/pipeline/bin/
RUN chmod +x /opt/pipeline/bin/*

# Add pipeline bin to PATH
ENV PATH="/opt/pipeline/bin:${PATH}"

# Set default command
CMD ["/bin/bash"]
