digraph "pipeline_dag_20250709_215527" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v0 -> v1;

v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v1 -> v2;

v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="flatten"];
v2 -> v3;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="flatten"];
v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="filter"];
v3 -> v4;

v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="filter"];
v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v4 -> v7 [label="input_samples"];

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v50 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v5 -> v50 [label="ch_versions"];

v6 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v51 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v6 -> v51 [label="ch_multiqc_files"];

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v54 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v7 -> v54 [label="ch_reads"];

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v16 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v7 -> v16 [label="ch_reads"];

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v86 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v7 -> v86 [label="ch_reads"];

v8 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v8 -> v19 [label="ch_versions"];

v9 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v9 -> v22 [label="ch_multiqc_files"];

v10 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v10 -> v11;

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v11 -> v28 [label="ch_fasta"];

v12 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v12 -> v13;

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v13 -> v28 [label="ch_fai"];

v14 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v14 -> v15;

v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v15 -> v28 [label="ch_dict"];

v16 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v17 [shape=point];
v16 -> v17;

v16 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v16 -> v20;

v16 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v16 -> v18;

v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v18 -> v19;

v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v19 -> v30 [label="ch_versions"];

v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v21 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v20 -> v21;

v21 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v21 -> v22;

v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v51 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v22 -> v51 [label="multiqc_files"];

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v23 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v7 -> v23 [label="ch_reads"];

v23 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v24 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v23 -> v24;

v23 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v23 -> v25;

v24 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v26 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v24 -> v26;

v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v26 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v25 -> v26;

v26 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v26 -> v27;

v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v27 -> v28 [label="ch_tumor_normal_pairs"];

v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v28 -> v31;

v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v28 -> v31;

v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v28 -> v32;

v28 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v28 -> v29;

v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v29 -> v30;

v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v30 -> v37 [label="ch_versions"];

v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v31 -> v32;

v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v32 -> v33 [label="ch_mutect2_for_filtering"];

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v11 -> v33 [label="ch_fasta"];

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v13 -> v33 [label="ch_fai"];

v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v15 -> v33 [label="ch_dict"];

v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v42 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v33 -> v42 [label="variants"];

v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v35 [shape=point];
v33 -> v35;

v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v34 [shape=point];
v33 -> v34;

v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v33 -> v36;

v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v36 -> v37;

v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v41 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v37 -> v41 [label="ch_versions"];

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v38 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v7 -> v38 [label="ch_reads"];

v38 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v38 -> v112 [label="hla_types"];

v38 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v39 [shape=point];
v38 -> v39;

v38 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v40 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v38 -> v40;

v40 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v41 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v40 -> v41;

v41 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v49 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v41 -> v49 [label="ch_versions"];

v42 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v42 -> v43;

v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v44 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v43 -> v44;

v44 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v45 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v44 -> v45 [label="ch_patient_variants"];

v45 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v47 [shape=point];
v45 -> v47 [label="merged_variants"];

v45 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v46 [shape=point];
v45 -> v46;

v45 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v48 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v45 -> v48;

v48 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v49 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v48 -> v49;

v49 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v50 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v49 -> v50 [label="versions"];

v50 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v82 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v50 -> v82 [label="ch_versions"];

v51 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v51 -> v83 [label="ch_multiqc_files"];

v52 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v57 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v52 -> v57 [label="ch_versions"];

v53 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v60 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v53 -> v60 [label="ch_multiqc_files"];

v54 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v55 [shape=point];
v54 -> v55;

v54 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v58 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v54 -> v58;

v54 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v56 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v54 -> v56;

v56 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v57 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v56 -> v57;

v57 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v67 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v57 -> v67 [label="ch_versions"];

v58 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v59 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v58 -> v59;

v59 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v60 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v59 -> v60;

v60 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v60 -> v74 [label="ch_multiqc_files"];

v61 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v62 [shape=point];
v61 -> v62 [label="ch_salmon_index"];

v63 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v65 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v63 -> v65;

v64 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v65 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v64 -> v65;

v65 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v68 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v65 -> v68 [label="index"];

v65 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v66 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v65 -> v66;

v66 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v67 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v66 -> v67;

v67 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v71 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v67 -> v71 [label="ch_versions"];

v68 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v69 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v68 -> v69;

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v69 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v7 -> v69 [label="ch_reads"];

v69 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v72 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v69 -> v72 [label="transcripts"];

v69 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v70 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v69 -> v70;

v70 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v71 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v70 -> v71;

v71 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v81 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v71 -> v81 [label="ch_versions"];

v72 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v72 -> v73;

v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v73 -> v74;

v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v74 -> v83 [label="multiqc_files"];

v69 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v75 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v69 -> v75 [label="transcripts"];

v75 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v76 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v75 -> v76;

v76 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v76 -> v77;

v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v78 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v77 -> v78 [label="ch_patient_transcripts"];

v78 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v79 [shape=point];
v78 -> v79 [label="merged_transcripts"];

v78 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v78 -> v80;

v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v81 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v80 -> v81;

v81 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v82 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v81 -> v82 [label="versions"];

v82 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v109 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v82 -> v109 [label="ch_versions"];

v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v110 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v83 -> v110 [label="ch_multiqc_files"];

v84 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v89 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v84 -> v89 [label="ch_versions"];

v85 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v92 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v85 -> v92 [label="ch_multiqc_files"];

v86 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v87 [shape=point];
v86 -> v87;

v86 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v90 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v86 -> v90;

v86 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v88 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v86 -> v88;

v88 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v89 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v88 -> v89;

v89 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v97 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v89 -> v97 [label="ch_versions"];

v90 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v91 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v90 -> v91;

v91 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v92 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v91 -> v92;

v92 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v110 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v92 -> v110 [label="multiqc_files"];

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v93 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v7 -> v93 [label="ch_reads"];

v93 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v98 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v93 -> v98 [label="raw_data"];

v93 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v95 [shape=point];
v93 -> v95;

v93 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v94 [shape=point];
v93 -> v94;

v93 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v96 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v93 -> v96;

v96 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v97 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v96 -> v97;

v97 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v100 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v97 -> v100 [label="ch_versions"];

v98 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v101 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v98 -> v101 [label="clonotypes"];

v98 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v99 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v98 -> v99;

v99 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v100 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v99 -> v100;

v100 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v108 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v100 -> v108 [label="ch_versions"];

v101 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v102 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v101 -> v102;

v102 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v102 -> v103;

v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v103 -> v104 [label="ch_patient_clonotypes"];

v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v106 [shape=point];
v104 -> v106 [label="tracking"];

v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v105 [shape=point];
v104 -> v105 [label="reports"];

v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v107 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v104 -> v107;

v107 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v108 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v107 -> v108;

v108 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v109 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v108 -> v109 [label="versions"];

v109 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v132 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v109 -> v132 [label="ch_versions"];

v110 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v134 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v110 -> v134 [label="ch_multiqc_files"];

v33 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v111 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v33 -> v111 [label="variants"];

v69 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v111 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v69 -> v111 [label="transcripts"];

v111 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v111 -> v112;

v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v115 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v112 -> v115 [label="ch_input"];

v113 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v113 -> v118 [label="ch_versions"];

v114 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v134 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v114 -> v134 [label="multiqc_files"];

v115 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v119 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v115 -> v119 [label="peptides"];

v115 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v116 [shape=point];
v115 -> v116;

v115 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v117 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v115 -> v117;

v117 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v117 -> v118;

v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v121 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v118 -> v121 [label="ch_versions"];

v119 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v122 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v119 -> v122 [label="predictions"];

v119 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v120 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v119 -> v120;

v120 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v121 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v120 -> v121;

v121 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v121 -> v124 [label="ch_versions"];

v122 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v122 -> v126 [label="filtered"];

v122 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v123 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v122 -> v123;

v123 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v123 -> v124;

v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v131 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v124 -> v131 [label="ch_versions"];

v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v125 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v112 -> v125 [label="ch_input"];

v125 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v125 -> v126;

v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v127 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v126 -> v127 [label="ch_prioritization_input"];

v127 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v129 [shape=point];
v127 -> v129 [label="neoantigens"];

v127 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v128 [shape=point];
v127 -> v128 [label="summary"];

v127 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v130 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v127 -> v130;

v130 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v131 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v130 -> v131;

v131 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v132 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v131 -> v132 [label="versions"];

v132 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v133 [shape=point];
v132 -> v133 [label="versions"];

v134 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v135 [shape=point];
v134 -> v135 [label="multiqc_files"];

}
