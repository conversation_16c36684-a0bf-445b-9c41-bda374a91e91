digraph "pipeline_dag_20250709_225451" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v0 -> v1;

v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="flatMap"];
v1 -> v2;

v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="flatMap"];
v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v2 -> v5 [label="input_samples"];

v3 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v53 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v3 -> v53 [label="ch_versions"];

v4 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v54 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v4 -> v54 [label="ch_multiqc_files"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v57 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v5 -> v57 [label="ch_reads"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v5 -> v14 [label="ch_reads"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v89 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v5 -> v89 [label="ch_reads"];

v6 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v6 -> v17 [label="ch_versions"];

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v7 -> v20 [label="ch_multiqc_files"];

v8 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v8 -> v9;

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v21 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX"];
v9 -> v21 [label="ch_fasta"];

v10 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v10 -> v11;

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v11 -> v34 [label="ch_fai"];

v12 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v12 -> v13;

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v13 -> v34 [label="ch_dict"];

v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v15 [shape=point];
v14 -> v15;

v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v14 -> v18;

v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v14 -> v16;

v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v16 -> v17;

v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v17 -> v22 [label="ch_versions"];

v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v18 -> v19;

v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v19 -> v20;

v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v54 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v20 -> v54 [label="multiqc_files"];

v21 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX"];
v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v21 -> v23;

v21 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v21 -> v22;

v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v22 -> v25 [label="ch_versions"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v5 -> v23 [label="ch_reads"];

v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v26 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX"];
v23 -> v26;

v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v24 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v23 -> v24;

v24 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v24 -> v25;

v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v25 -> v28 [label="ch_versions"];

v26 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX"];
v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v26 -> v29;

v26 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX"];
v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v26 -> v27;

v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v27 -> v28;

v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v28 -> v36 [label="ch_versions"];

v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v29 -> v30;

v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v29 -> v31;

v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v30 -> v32;

v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v31 -> v32;

v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v33 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v32 -> v33;

v33 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v33 -> v34 [label="ch_tumor_normal_pairs"];

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v9 -> v34 [label="ch_fasta"];

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v34 -> v37;

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v34 -> v37;

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v38 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v34 -> v38;

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v35 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v34 -> v35;

v35 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v35 -> v36;

v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v36 -> v43 [label="ch_versions"];

v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v38 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v37 -> v38;

v38 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v38 -> v39 [label="ch_mutect2_for_filtering"];

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v9 -> v39 [label="ch_fasta"];

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v11 -> v39 [label="ch_fai"];

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v13 -> v39 [label="ch_dict"];

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v45 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v39 -> v45 [label="variants"];

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v41 [shape=point];
v39 -> v41;

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v40 [shape=point];
v39 -> v40;

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v42 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v39 -> v42;

v42 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v42 -> v43;

v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v52 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v43 -> v52 [label="ch_versions"];

v44 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v44 -> v115 [label="hla_types"];

v45 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v46 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v45 -> v46;

v46 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v47 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v46 -> v47;

v47 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v48 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v47 -> v48 [label="ch_patient_variants"];

v48 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v50 [shape=point];
v48 -> v50 [label="merged_variants"];

v48 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v49 [shape=point];
v48 -> v49;

v48 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v51 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v48 -> v51;

v51 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v52 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v51 -> v52;

v52 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v53 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v52 -> v53 [label="versions"];

v53 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v85 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v53 -> v85 [label="ch_versions"];

v54 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v86 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v54 -> v86 [label="ch_multiqc_files"];

v55 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v60 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v55 -> v60 [label="ch_versions"];

v56 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v63 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v56 -> v63 [label="ch_multiqc_files"];

v57 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v58 [shape=point];
v57 -> v58;

v57 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v61 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v57 -> v61;

v57 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v59 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v57 -> v59;

v59 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v60 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v59 -> v60;

v60 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v70 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v60 -> v70 [label="ch_versions"];

v61 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v62 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v61 -> v62;

v62 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v63 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v62 -> v63;

v63 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v63 -> v77 [label="ch_multiqc_files"];

v64 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v65 [shape=point];
v64 -> v65 [label="ch_salmon_index"];

v66 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v68 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v66 -> v68;

v67 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v68 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v67 -> v68;

v68 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v71 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v68 -> v71 [label="index"];

v68 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v69 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v68 -> v69;

v69 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v70 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v69 -> v70;

v70 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v70 -> v74 [label="ch_versions"];

v71 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v72 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v71 -> v72;

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v72 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v5 -> v72 [label="ch_reads"];

v72 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v75 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v72 -> v75 [label="transcripts"];

v72 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v72 -> v73;

v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v73 -> v74;

v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v84 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v74 -> v84 [label="ch_versions"];

v75 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v76 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v75 -> v76;

v76 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v76 -> v77;

v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v86 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v77 -> v86 [label="multiqc_files"];

v72 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v78 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v72 -> v78 [label="transcripts"];

v78 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v79 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v78 -> v79;

v79 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v79 -> v80;

v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v81 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v80 -> v81 [label="ch_patient_transcripts"];

v81 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v82 [shape=point];
v81 -> v82 [label="merged_transcripts"];

v81 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v81 -> v83;

v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v84 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v83 -> v84;

v84 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v85 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v84 -> v85 [label="versions"];

v85 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v85 -> v112 [label="ch_versions"];

v86 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v113 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v86 -> v113 [label="ch_multiqc_files"];

v87 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v92 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v87 -> v92 [label="ch_versions"];

v88 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v95 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v88 -> v95 [label="ch_multiqc_files"];

v89 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v90 [shape=point];
v89 -> v90;

v89 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v93 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v89 -> v93;

v89 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v91 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v89 -> v91;

v91 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v92 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v91 -> v92;

v92 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v100 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v92 -> v100 [label="ch_versions"];

v93 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v94 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v93 -> v94;

v94 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v95 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v94 -> v95;

v95 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v113 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v95 -> v113 [label="multiqc_files"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v96 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v5 -> v96 [label="ch_reads"];

v96 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v101 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v96 -> v101 [label="raw_data"];

v96 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v98 [shape=point];
v96 -> v98;

v96 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v97 [shape=point];
v96 -> v97;

v96 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v99 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v96 -> v99;

v99 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v100 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v99 -> v100;

v100 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v100 -> v103 [label="ch_versions"];

v101 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v104 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v101 -> v104 [label="clonotypes"];

v101 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v102 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v101 -> v102;

v102 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v102 -> v103;

v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v111 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v103 -> v111 [label="ch_versions"];

v104 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v105 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v104 -> v105;

v105 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v106 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v105 -> v106;

v106 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v107 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v106 -> v107 [label="ch_patient_clonotypes"];

v107 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v109 [shape=point];
v107 -> v109 [label="tracking"];

v107 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v108 [shape=point];
v107 -> v108 [label="reports"];

v107 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v110 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v107 -> v110;

v110 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v111 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v110 -> v111;

v111 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v111 -> v112 [label="versions"];

v112 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v135 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v112 -> v135 [label="ch_versions"];

v113 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v137 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v113 -> v137 [label="ch_multiqc_files"];

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v114 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v39 -> v114 [label="variants"];

v72 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v114 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v72 -> v114 [label="transcripts"];

v114 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v114 -> v115;

v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v118 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v115 -> v118 [label="ch_input"];

v116 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v121 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v116 -> v121 [label="ch_versions"];

v117 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v137 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v117 -> v137 [label="multiqc_files"];

v118 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v122 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v118 -> v122 [label="peptides"];

v118 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v119 [shape=point];
v118 -> v119;

v118 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v120 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v118 -> v120;

v120 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v121 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v120 -> v121;

v121 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v121 -> v124 [label="ch_versions"];

v122 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v125 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v122 -> v125 [label="predictions"];

v122 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v123 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v122 -> v123;

v123 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v123 -> v124;

v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v127 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v124 -> v127 [label="ch_versions"];

v125 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v129 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v125 -> v129 [label="filtered"];

v125 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v125 -> v126;

v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v127 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v126 -> v127;

v127 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v134 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v127 -> v134 [label="ch_versions"];

v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v128 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v115 -> v128 [label="ch_input"];

v128 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v129 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v128 -> v129;

v129 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v130 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v129 -> v130 [label="ch_prioritization_input"];

v130 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v132 [shape=point];
v130 -> v132 [label="neoantigens"];

v130 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v131 [shape=point];
v130 -> v131 [label="summary"];

v130 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v133 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v130 -> v133;

v133 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v134 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v133 -> v134;

v134 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v135 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v134 -> v135 [label="versions"];

v135 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v136 [shape=point];
v135 -> v136 [label="versions"];

v137 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v138 [shape=point];
v137 -> v138 [label="multiqc_files"];

}
