digraph "pipeline_dag_20250709_231809" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v0 -> v1;

v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="flatMap"];
v1 -> v2;

v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="flatMap"];
v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v2 -> v5 [label="input_samples"];

v3 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v56 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v3 -> v56 [label="ch_versions"];

v4 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v57 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v4 -> v57 [label="ch_multiqc_files"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v92 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v5 -> v92 [label="ch_reads"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v5 -> v14 [label="ch_reads"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v60 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v5 -> v60 [label="ch_reads"];

v6 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v6 -> v17 [label="ch_versions"];

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v7 -> v20 [label="ch_multiqc_files"];

v8 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v8 -> v9;

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v21 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX"];
v9 -> v21 [label="ch_fasta"];

v10 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v10 -> v11;

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v11 -> v34 [label="ch_fai"];

v12 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v12 -> v13;

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v13 -> v34 [label="ch_dict"];

v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v15 [shape=point];
v14 -> v15;

v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v14 -> v18;

v14 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FASTQC"];
v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v14 -> v16;

v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v16 -> v17;

v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v17 -> v22 [label="ch_versions"];

v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v18 -> v19;

v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v19 -> v20;

v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v57 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v20 -> v57 [label="multiqc_files"];

v21 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX"];
v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v21 -> v23;

v21 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_INDEX"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v21 -> v22;

v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v22 -> v25 [label="ch_versions"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v5 -> v23 [label="ch_reads"];

v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v26 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX"];
v23 -> v26;

v23 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM"];
v24 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v23 -> v24;

v24 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v24 -> v25;

v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v25 -> v28 [label="ch_versions"];

v26 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX"];
v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v26 -> v29;

v26 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:SAMTOOLS_INDEX"];
v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v26 -> v27;

v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v27 -> v28;

v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v28 -> v36 [label="ch_versions"];

v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v29 -> v30;

v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v29 -> v31;

v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v30 -> v32;

v31 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v31 -> v32;

v32 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v33 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v32 -> v33;

v33 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v33 -> v34 [label="ch_tumor_normal_pairs"];

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v9 -> v34 [label="ch_fasta"];

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v34 -> v37;

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v34 -> v37;

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v38 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v34 -> v38;

v34 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2"];
v35 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v34 -> v35;

v35 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v35 -> v36;

v36 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v36 -> v43 [label="ch_versions"];

v37 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v38 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v37 -> v38;

v38 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v38 -> v39 [label="ch_mutect2_for_filtering"];

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v9 -> v39 [label="ch_fasta"];

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v11 -> v39 [label="ch_fai"];

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v13 -> v39 [label="ch_dict"];

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v48 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v39 -> v48 [label="variants"];

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v41 [shape=point];
v39 -> v41;

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v40 [shape=point];
v39 -> v40;

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v42 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v39 -> v42;

v42 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v42 -> v43;

v43 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v47 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v43 -> v47 [label="ch_versions"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v44 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v5 -> v44 [label="ch_reads"];

v44 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v44 -> v118 [label="hla_types"];

v44 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v45 [shape=point];
v44 -> v45;

v44 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:OPTITYPE"];
v46 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v44 -> v46;

v46 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v47 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v46 -> v47;

v47 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v55 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v47 -> v55 [label="ch_versions"];

v48 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v49 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v48 -> v49;

v49 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v50 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v49 -> v50;

v50 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v51 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v50 -> v51 [label="ch_patient_variants"];

v51 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v53 [shape=point];
v51 -> v53 [label="merged_variants"];

v51 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v52 [shape=point];
v51 -> v52;

v51 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MERGE_VARIANTS"];
v54 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v51 -> v54;

v54 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v55 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v54 -> v55;

v55 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v56 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v55 -> v56 [label="versions"];

v56 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v88 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v56 -> v88 [label="ch_versions"];

v57 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v89 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v57 -> v89 [label="ch_multiqc_files"];

v58 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v63 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v58 -> v63 [label="ch_versions"];

v59 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v66 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v59 -> v66 [label="ch_multiqc_files"];

v60 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v61 [shape=point];
v60 -> v61;

v60 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v64 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v60 -> v64;

v60 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:FASTQC"];
v62 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v60 -> v62;

v62 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v63 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v62 -> v63;

v63 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v63 -> v73 [label="ch_versions"];

v64 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v65 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v64 -> v65;

v65 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v66 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v65 -> v66;

v66 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v66 -> v80 [label="ch_multiqc_files"];

v67 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v68 [shape=point];
v67 -> v68 [label="ch_salmon_index"];

v69 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v71 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v69 -> v71;

v70 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v71 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v70 -> v71;

v71 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v71 -> v74 [label="index"];

v71 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_INDEX"];
v72 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v71 -> v72;

v72 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v72 -> v73;

v73 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v73 -> v77 [label="ch_versions"];

v74 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v75 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v74 -> v75;

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v75 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v5 -> v75 [label="ch_reads"];

v75 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v78 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v75 -> v78 [label="transcripts"];

v75 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v76 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v75 -> v76;

v76 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v76 -> v77;

v77 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v87 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v77 -> v87 [label="ch_versions"];

v78 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v79 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v78 -> v79;

v79 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v79 -> v80;

v80 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v89 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v80 -> v89 [label="multiqc_files"];

v75 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v81 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v75 -> v81 [label="transcripts"];

v81 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v82 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v81 -> v82;

v82 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v82 -> v83;

v83 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v84 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v83 -> v84 [label="ch_patient_transcripts"];

v84 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v85 [shape=point];
v84 -> v85 [label="merged_transcripts"];

v84 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:MERGE_TRANSCRIPTS"];
v86 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v84 -> v86;

v86 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v87 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v86 -> v87;

v87 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v88 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v87 -> v88 [label="versions"];

v88 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v88 -> v115 [label="ch_versions"];

v89 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v116 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v89 -> v116 [label="ch_multiqc_files"];

v90 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v95 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v90 -> v95 [label="ch_versions"];

v91 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v98 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v91 -> v98 [label="ch_multiqc_files"];

v92 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v93 [shape=point];
v92 -> v93;

v92 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v96 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v92 -> v96;

v92 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:FASTQC"];
v94 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v92 -> v94;

v94 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v95 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v94 -> v95;

v95 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v95 -> v103 [label="ch_versions"];

v96 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v97 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v96 -> v97;

v97 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="ifEmpty"];
v98 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v97 -> v98;

v98 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v116 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v98 -> v116 [label="multiqc_files"];

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="branch"];
v99 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v5 -> v99 [label="ch_reads"];

v99 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v99 -> v104 [label="raw_data"];

v99 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v101 [shape=point];
v99 -> v101;

v99 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v100 [shape=point];
v99 -> v100;

v99 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_ANALYZE"];
v102 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v99 -> v102;

v102 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v102 -> v103;

v103 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v106 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v103 -> v106 [label="ch_versions"];

v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v107 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v104 -> v107 [label="clonotypes"];

v104 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:MIXCR_EXPORTCLONES"];
v105 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v104 -> v105;

v105 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v106 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v105 -> v106;

v106 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v114 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v106 -> v114 [label="ch_versions"];

v107 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v108 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v107 -> v108;

v108 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v109 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v108 -> v109;

v109 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v110 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v109 -> v110 [label="ch_patient_clonotypes"];

v110 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v112 [shape=point];
v110 -> v112 [label="tracking"];

v110 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v111 [shape=point];
v110 -> v111 [label="reports"];

v110 [label="IMMUNE_NEOANTIGEN_PIPELINE:TCR_WORKFLOW:TRACK_CLONES"];
v113 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v110 -> v113;

v113 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v114 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v113 -> v114;

v114 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v114 -> v115 [label="versions"];

v115 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v138 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v115 -> v138 [label="ch_versions"];

v116 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v140 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v116 -> v140 [label="ch_multiqc_files"];

v75 [label="IMMUNE_NEOANTIGEN_PIPELINE:RNASEQ_WORKFLOW:SALMON_QUANT"];
v117 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v75 -> v117 [label="transcripts"];

v39 [label="IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:FILTERMUTECTCALLS"];
v117 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v39 -> v117 [label="variants"];

v117 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v117 -> v118;

v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v121 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v118 -> v121 [label="ch_input"];

v119 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v119 -> v124 [label="ch_versions"];

v120 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v140 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v120 -> v140 [label="multiqc_files"];

v121 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v125 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v121 -> v125 [label="peptides"];

v121 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v122 [shape=point];
v121 -> v122;

v121 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:GENERATE_PEPTIDES"];
v123 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v121 -> v123;

v123 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v123 -> v124;

v124 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v127 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v124 -> v127 [label="ch_versions"];

v125 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v128 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v125 -> v128 [label="predictions"];

v125 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:NETMHCPAN"];
v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v125 -> v126;

v126 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v127 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v126 -> v127;

v127 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v130 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v127 -> v130 [label="ch_versions"];

v128 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v132 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v128 -> v132 [label="filtered"];

v128 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:FILTER_NEOANTIGENS"];
v129 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v128 -> v129;

v129 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v130 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v129 -> v130;

v130 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v137 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v130 -> v137 [label="ch_versions"];

v118 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v131 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v118 -> v131 [label="ch_input"];

v131 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v132 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v131 -> v132;

v132 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="join"];
v133 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v132 -> v133 [label="ch_prioritization_input"];

v133 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v135 [shape=point];
v133 -> v135 [label="neoantigens"];

v133 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v134 [shape=point];
v133 -> v134 [label="summary"];

v133 [label="IMMUNE_NEOANTIGEN_PIPELINE:NEOANTIGEN_WORKFLOW:PRIORITIZE_NEOANTIGENS"];
v136 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v133 -> v136;

v136 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v137 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v136 -> v137;

v137 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v138 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v137 -> v138 [label="versions"];

v138 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v139 [shape=point];
v138 -> v139 [label="versions"];

v140 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v141 [shape=point];
v140 -> v141 [label="multiqc_files"];

}
