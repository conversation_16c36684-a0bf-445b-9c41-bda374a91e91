digraph "pipeline_dag_20250709_234252" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v0 -> v1;

v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="splitCsv"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v1 -> v2;

v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN"];
v2 -> v4 [label="ch_reads"];

v3 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v3 -> v7 [label="ch_versions"];

v4 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN"];
v8 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE"];
v4 -> v8 [label="vdjca"];

v4 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN"];
v5 [shape=point];
v4 -> v5 [label="align_reports"];

v4 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ALIGN"];
v6 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v4 -> v6;

v6 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v6 -> v7;

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v7 -> v11 [label="ch_versions"];

v8 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE"];
v12 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER"];
v8 -> v12 [label="clns"];

v8 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE"];
v9 [shape=point];
v8 -> v9 [label="assemble_reports"];

v8 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_ASSEMBLE"];
v10 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v8 -> v10;

v10 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v10 -> v11;

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v11 -> v15 [label="ch_versions"];

v12 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER"];
v16 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES"];
v12 -> v16 [label="clns_filtered"];

v12 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER"];
v13 [shape=point];
v12 -> v13 [label="filter_reports"];

v12 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_FILTER"];
v14 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v12 -> v14;

v14 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v14 -> v15;

v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v15 -> v19 [label="ch_versions"];

v16 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES"];
v17 [shape=point];
v16 -> v17 [label="clonotypes"];

v16 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES"];
v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v16 -> v20 [label="clonotypes_tsv"];

v16 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:MIXCR_EXPORTCLONES"];
v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v16 -> v18;

v18 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v18 -> v19;

v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v19 -> v30 [label="ch_versions"];

v20 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v21 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v20 -> v21;

v21 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="groupTuple"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v21 -> v22;

v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v22 -> v23 [label="ch_clonotypes_grouped"];

v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v28 [shape=point];
v23 -> v28 [label="diversity"];

v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v27 [shape=point];
v23 -> v27 [label="expansion"];

v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v26 [shape=point];
v23 -> v26 [label="tracking"];

v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v25 [shape=point];
v23 -> v25 [label="plots"];

v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v24 [shape=point];
v23 -> v24 [label="report"];

v23 [label="NFCORE_TCRLONGITUDINAL:TCR_LONGITUDINAL:IMMUNARCH_ANALYSIS"];
v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v23 -> v29;

v29 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v29 -> v30;

v30 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="mix"];
v31 [shape=point];
v30 -> v31 [label="versions"];

v32 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.empty"];
v33 [shape=point];
v32 -> v33 [label="multiqc_report"];

}
