WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
05:34:08.726 INFO  NativeLibraryLoader - Loading libgkl_compression.so from jar:file:/gatk/gatk-package-*******-local.jar!/com/intel/gkl/native/libgkl_compression.so
05:34:08.870 INFO  Mutect2 - ------------------------------------------------------------
05:34:08.871 INFO  Mutect2 - The Genome Analysis Toolkit (GATK) v*******
05:34:08.871 INFO  Mutect2 - For support and documentation go to https://software.broadinstitute.org/gatk/
05:34:08.871 INFO  Mutect2 - Executing as root@6069ad716945 on Linux v6.10.14-linuxkit amd64
05:34:08.871 INFO  Mutect2 - Java runtime: OpenJDK 64-Bit Server VM v1.8.0_242-8u242-b08-0ubuntu3~18.04-b08
05:34:08.871 INFO  Mutect2 - Start Date/Time: July 10, 2025 5:34:08 AM GMT
05:34:08.871 INFO  Mutect2 - ------------------------------------------------------------
05:34:08.871 INFO  Mutect2 - ------------------------------------------------------------
05:34:08.873 INFO  Mutect2 - HTSJDK Version: 3.0.1
05:34:08.873 INFO  Mutect2 - Picard Version: 2.27.5
05:34:08.873 INFO  Mutect2 - Built for Spark Version: 2.4.5
05:34:08.873 INFO  Mutect2 - HTSJDK Defaults.COMPRESSION_LEVEL : 2
05:34:08.873 INFO  Mutect2 - HTSJDK Defaults.USE_ASYNC_IO_READ_FOR_SAMTOOLS : false
05:34:08.873 INFO  Mutect2 - HTSJDK Defaults.USE_ASYNC_IO_WRITE_FOR_SAMTOOLS : true
05:34:08.873 INFO  Mutect2 - HTSJDK Defaults.USE_ASYNC_IO_WRITE_FOR_TRIBBLE : false
05:34:08.873 INFO  Mutect2 - Deflater: IntelDeflater
05:34:08.873 INFO  Mutect2 - Inflater: IntelInflater
05:34:08.873 INFO  Mutect2 - GCS max retries/reopens: 20
05:34:08.873 INFO  Mutect2 - Requester pays: disabled
05:34:08.874 INFO  Mutect2 - Initializing engine
05:34:09.199 INFO  Mutect2 - Shutting down engine
[July 10, 2025 5:34:09 AM GMT] org.broadinstitute.hellbender.tools.walkers.mutect.Mutect2 done. Elapsed time: 0.01 minutes.
Runtime.totalMemory()=283639808
***********************************************************************

A USER ERROR has occurred: Input files reference and reads have incompatible contigs: No overlapping contigs found.
  reference contigs = [chr22]
  reads contigs = []

***********************************************************************
Set the system property GATK_STACKTRACE_ON_USER_EXCEPTION (--java-options '-DGATK_STACKTRACE_ON_USER_EXCEPTION=true') to print the stack trace.
Using GATK jar /gatk/gatk-package-*******-local.jar
Running:
    java -Dsamjdk.use_async_io_read_samtools=false -Dsamjdk.use_async_io_write_samtools=true -Dsamjdk.use_async_io_write_tribble=false -Dsamjdk.compression_level=2 -jar /gatk/gatk-package-*******-local.jar Mutect2 --input SAMPLE_01_T_wes_1.fastq.gz --input SAMPLE_01_T_wes_2.fastq.gz --input SAMPLE_01_N_wes_1.fastq.gz --input SAMPLE_01_N_wes_2.fastq.gz --reference genome.fasta --output PATIENT_01.vcf.gz --tumor-sample PATIENT_01_tumor --normal-sample PATIENT_01_normal
