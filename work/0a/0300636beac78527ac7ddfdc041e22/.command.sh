#!/bin/bash -euo pipefail
gatk Mutect2 \
    --input SAMPLE_01_cfDNA_wes_1.fastq.gz --input SAMPLE_01_cfDNA_wes_2.fastq.gz \
    --input SAMPLE_01_N_wes_1.fastq.gz --input SAMPLE_01_N_wes_2.fastq.gz \
    --reference genome.fasta \
    --output PATIENT_01.vcf.gz \
    --tumor-sample PATIENT_01_tumor \
    --normal-sample PATIENT_01_normal \


cat <<-END_VERSIONS > versions.yml
"IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:MUTECT2":
    gatk4: $(echo $(gatk --version 2>&1) | sed 's/^.*(GATK) v//; s/ .*$//')
END_VERSIONS
