WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
Version Info: ### PLEASE UPGRADE SALMON ###
### A newer version of salmon with important bug fixes and improvements is available. ####
###
The newest version, available at https://github.com/COMBINE-lab/salmon/releases
contains new features, improvements, and bug fixes; please upgrade at your
earliest convenience.
###
Sign up for the salmon mailing list to hear about new versions, features and updates at:
https://oceangenomics.com/subscribe
### salmon (selective-alignment-based) v1.9.0
### [ program ] => salmon 
### [ command ] => quant 
### [ threads ] => { 2 }
### [ libType ] => { A }
### [ index ] => { salmon }
### [ mates1 ] => { SAMPLE_01_T_rna_1.fastq.gz }
### [ mates2 ] => { SAMPLE_01_T_rna_2.fastq.gz }
### [ output ] => { SAMPLE_01_T }
Logs will be written to SAMPLE_01_T/logs
[2025-07-10 05:42:24.340] [jointLog] [info] setting maxHashResizeThreads to 2
[2025-07-10 05:42:24.341] [jointLog] [info] Fragment incompatibility prior below threshold.  Incompatible fragments will be ignored.
[2025-07-10 05:42:24.341] [jointLog] [info] Usage of --validateMappings implies use of minScoreFraction. Since not explicitly specified, it is being set to 0.65
[2025-07-10 05:42:24.341] [jointLog] [info] Setting consensusSlack to selective-alignment default of 0.35.
[2025-07-10 05:42:24.341] [jointLog] [info] parsing read library format
[2025-07-10 05:42:24.343] [jointLog] [info] There is 1 library.
-----------------------------------------
| Loading contig table | Time = 1.4223 ms
-----------------------------------------
size = 83
-----------------------------------------
| Loading contig offsets | Time = 1.397 ms
-----------------------------------------
-----------------------------------------
| Loading reference lengths | Time = 193.79 us
-----------------------------------------
-----------------------------------------
| Loading mphf table | Time = 2.07 ms
-----------------------------------------
size = 42218
Number of ones: 82
Number of ones per inventory item: 512
Inventory entries filled: 1
-----------------------------------------
| Loading contig boundaries | Time = 1.3447 ms
-----------------------------------------
size = 42218
-----------------------------------------
| Loading sequence | Time = 776.88 us
-----------------------------------------
size = 39758
-----------------------------------------
| Loading positions | Time = 1.4847 ms
-----------------------------------------
size = 40001
-----------------------------------------
| Loading reference sequence | Time = 940.21 us
-----------------------------------------
-----------------------------------------
| Loading reference accumulative lengths | Time = 86 us
-----------------------------------------
[2025-07-10 05:42:24.361] [jointLog] [info] Loading pufferfish index
[2025-07-10 05:42:24.362] [jointLog] [info] Loading dense pufferfish index.
[2025-07-10 05:42:24.377] [jointLog] [info] done
[2025-07-10 05:42:24.415] [jointLog] [info] Index contained 1 targets
[2025-07-10 05:42:24.416] [jointLog] [info] Number of decoys : 1
[2025-07-10 05:42:24.416] [jointLog] [info] First decoy index : 0 




[2025-07-10 05:42:24.494] [jointLog] [warning] salmon was only able to assign 0 fragments to transcripts in the index, but the minimum number of required assigned fragments (--minAssignedFrags) was 10. This could be indicative of a mismatch between the reference and sample, or a very bad sample.  You can change the --minAssignedFrags parameter to force salmon to quantify with fewer assigned fragments (must have at least 1).
