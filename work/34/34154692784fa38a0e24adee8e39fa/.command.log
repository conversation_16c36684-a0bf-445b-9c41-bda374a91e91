WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
Version Info: ### PLEASE UPGRADE SALMON ###
### A newer version of salmon with important bug fixes and improvements is available. ####
###
The newest version, available at https://github.com/COMBINE-lab/salmon/releases
contains new features, improvements, and bug fixes; please upgrade at your
earliest convenience.
###
Sign up for the salmon mailing list to hear about new versions, features and updates at:
https://oceangenomics.com/subscribe
### salmon (selective-alignment-based) v1.9.0
### [ program ] => salmon 
### [ command ] => quant 
### [ threads ] => { 2 }
### [ libType ] => { A }
### [ index ] => { salmon }
### [ mates1 ] => { SAMPLE_01_T_rna_1.fastq.gz }
### [ mates2 ] => { SAMPLE_01_T_rna_2.fastq.gz }
### [ output ] => { SAMPLE_01_T }
Logs will be written to SAMPLE_01_T/logs
[2025-07-10 05:34:09.602] [jointLog] [info] setting maxHashResizeThreads to 2
[2025-07-10 05:34:09.603] [jointLog] [info] Fragment incompatibility prior below threshold.  Incompatible fragments will be ignored.
[2025-07-10 05:34:09.604] [jointLog] [info] Usage of --validateMappings implies use of minScoreFraction. Since not explicitly specified, it is being set to 0.65
[2025-07-10 05:34:09.604] [jointLog] [info] Setting consensusSlack to selective-alignment default of 0.35.
[2025-07-10 05:34:09.605] [jointLog] [info] parsing read library format
[2025-07-10 05:34:09.610] [jointLog] [info] There is 1 library.
[2025-07-10 05:34:09.619] [jointLog] [info] Loading pufferfish index
[2025-07-10 05:34:09.620] [jointLog] [info] Loading dense pufferfish index.
-----------------------------------------
| Loading contig table | Time = 1.6231 ms
-----------------------------------------
size = 83
-----------------------------------------
| Loading contig offsets | Time = 1.096 ms
-----------------------------------------
-----------------------------------------
| Loading reference lengths | Time = 143.42 us
-----------------------------------------
-----------------------------------------
| Loading mphf table | Time = 1.3183 ms
-----------------------------------------
size = 42218
Number of ones: 82
Number of ones per inventory item: 512
Inventory entries filled: 1
-----------------------------------------
| Loading contig boundaries | Time = 1.8612 ms
-----------------------------------------
size = 42218
-----------------------------------------
| Loading sequence | Time = 1.0295 ms
-----------------------------------------
size = 39758
-----------------------------------------
| Loading positions | Time = 1.0939 ms
-----------------------------------------
size = 40001
-----------------------------------------
| Loading reference sequence | Time = 602.54 us
-----------------------------------------
-----------------------------------------
| Loading reference accumulative lengths | Time = 172.5 us
-----------------------------------------
[2025-07-10 05:34:09.637] [jointLog] [info] done
[2025-07-10 05:34:09.680] [jointLog] [info] Index contained 1 targets
[2025-07-10 05:34:09.680] [jointLog] [info] Number of decoys : 1
[2025-07-10 05:34:09.680] [jointLog] [info] First decoy index : 0 




[2025-07-10 05:34:09.760] [jointLog] [warning] salmon was only able to assign 0 fragments to transcripts in the index, but the minimum number of required assigned fragments (--minAssignedFrags) was 10. This could be indicative of a mismatch between the reference and sample, or a very bad sample.  You can change the --minAssignedFrags parameter to force salmon to quantify with fewer assigned fragments (must have at least 1).
