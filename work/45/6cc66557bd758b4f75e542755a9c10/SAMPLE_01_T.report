Analysis date: Thu Jul 10 05:54:55 UTC 2025
Input file(s): SAMPLE_01_T_tcr_1.fastq.gz,SAMPLE_01_T_tcr_2.fastq.gz
Output file(s): SAMPLE_01_T.vdjca
Version: 3.0.3; built=Sun Nov 18 15:48:30 UTC 2018; rev=5281a0f; lib=repseqio.v1.5
Command line arguments: -OallowPartialAlignments=true --species hsa --report SAMPLE_01_T.report -p rna-seq -OvParameters.geneFeatureToAlign=VTranscriptWithP SAMPLE_01_T_tcr_1.fastq.gz SAMPLE_01_T_tcr_2.fastq.gz SAMPLE_01_T.vdjca
Analysis time: 37.68s
Total sequencing reads: 266736
Successfully aligned reads: 0 (0%)
Alignment failed, no hits (not TCR/IG?): 261213 (97.93%)
Alignment failed because of absence of CDR3 parts: 521 (0.2%)
Alignment failed because of low total score: 5002 (1.88%)
Overlapped: 34923 (13.09%)
Overlapped and aligned: 0 (0%)
Alignment-aided overlaps: 0 (?%)
Overlapped and not aligned: 34923 (13.09%)
======================================
Analysis date: Thu Jul 10 05:55:33 UTC 2025
Input file(s): SAMPLE_01_T.vdjca
Output file(s): SAMPLE_01_T.rescued_0.vdjca
Version: 3.0.3; built=Sun Nov 18 15:48:30 UTC 2018; rev=5281a0f; lib=repseqio.v1.5
Command line arguments: --report SAMPLE_01_T.report SAMPLE_01_T.vdjca SAMPLE_01_T.rescued_0.vdjca
Analysis time: 188.00ms
Total alignments analysed: 0
Number of output alignments: 0 (?%)
Alignments already with CDR3 (no overlapping is performed): 0 (?%)
Successfully overlapped alignments: 0 (?%)
Left parts with too small N-region (failed to extract k-mer): 0 (?%)
Extracted k-mer diversity: 0
Dropped due to wildcard in k-mer: 0 (?%)
Dropped due to too short NRegion parts in overlap: 0 (?%)
Dropped overlaps with empty N region due to no complete NDN coverage: 0 (?%)
Number of left-side alignments: 0 (?%)
Number of right-side alignments: 0 (?%)
Complex overlaps: 0 (?%)
Over-overlaps: 0 (?%)
Partial alignments written to output: 0 (?%)
======================================
Analysis date: Thu Jul 10 05:55:33 UTC 2025
Input file(s): SAMPLE_01_T.rescued_0.vdjca
Output file(s): SAMPLE_01_T.rescued_1.vdjca
Version: 3.0.3; built=Sun Nov 18 15:48:30 UTC 2018; rev=5281a0f; lib=repseqio.v1.5
Command line arguments: --report SAMPLE_01_T.report SAMPLE_01_T.rescued_0.vdjca SAMPLE_01_T.rescued_1.vdjca
Analysis time: 57.00ms
Total alignments analysed: 0
Number of output alignments: 0 (?%)
Alignments already with CDR3 (no overlapping is performed): 0 (?%)
Successfully overlapped alignments: 0 (?%)
Left parts with too small N-region (failed to extract k-mer): 0 (?%)
Extracted k-mer diversity: 0
Dropped due to wildcard in k-mer: 0 (?%)
Dropped due to too short NRegion parts in overlap: 0 (?%)
Dropped overlaps with empty N region due to no complete NDN coverage: 0 (?%)
Number of left-side alignments: 0 (?%)
Number of right-side alignments: 0 (?%)
Complex overlaps: 0 (?%)
Over-overlaps: 0 (?%)
Partial alignments written to output: 0 (?%)
======================================
Analysis date: Thu Jul 10 05:55:33 UTC 2025
Input file(s): SAMPLE_01_T.rescued_1.vdjca
Output file(s): SAMPLE_01_T.extended.vdjca
Version: 3.0.3; built=Sun Nov 18 15:48:30 UTC 2018; rev=5281a0f; lib=repseqio.v1.5
Command line arguments: --report SAMPLE_01_T.report SAMPLE_01_T.rescued_1.vdjca SAMPLE_01_T.extended.vdjca
Analysis time: 9.00ms
Extended alignments count: 0 (?%)
V extensions total: 0 (?%)
V extensions with merged targets: 0 (?%)
J extensions total: 0 (?%)
J extensions with merged targets: 0 (?%)
V+J extensions: 0 (?%)
Mean V extension length: NaN
Mean J extension length: NaN
======================================
Analysis date: Thu Jul 10 05:55:33 UTC 2025
Input file(s): SAMPLE_01_T.extended.vdjca
Output file(s): SAMPLE_01_T.clna
Version: 3.0.3; built=Sun Nov 18 15:48:30 UTC 2018; rev=5281a0f; lib=repseqio.v1.5
Command line arguments: --report SAMPLE_01_T.report --write-alignments -OseparateByV=true -OseparateByJ=true -OseparateByC=true SAMPLE_01_T.extended.vdjca SAMPLE_01_T.clna
Analysis time: 102.00ms
Final clonotype count: 0
Average number of reads per clonotype: ?
Reads used in clonotypes, percent of total: 0 (0%)
Reads used in clonotypes before clustering, percent of total: 0 (0%)
Number of reads used as a core, percent of used: 0 (?%)
Mapped low quality reads, percent of used: 0 (?%)
Reads clustered in PCR error correction, percent of used: 0 (?%)
Reads pre-clustered due to the similar VJC-lists, percent of used: 0 (?%)
Reads dropped due to the lack of a clone sequence: 0 (0%)
Reads dropped due to low quality: 0 (0%)
Reads dropped due to failed mapping: 0 (0%)
Reads dropped with low quality clones: 0 (?%)
Clonotypes eliminated by PCR error correction: 0
Clonotypes dropped as low quality: 0
Clonotypes pre-clustered due to the similar VJC-lists: 0
======================================
