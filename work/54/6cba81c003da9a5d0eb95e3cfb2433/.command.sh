#!/bin/bash -euo pipefail
INDEX=`find -L ./ -name "*.amb" | sed 's/.amb//'`

bwa mem \
     \
    -t 2 \
    -R "@RG\tID:SAMPLE_01_N\tSM:SAMPLE_01_N\tPL:ILLUMINA" \
    $INDEX \
    SAMPLE_01_N_wes_1.fastq.gz \
    SAMPLE_01_N_wes_2.fastq.gz \
    | samtools sort -@ 2 -o SAMPLE_01_N.bam -

cat <<-END_VERSIONS > versions.yml
"IMMUNE_NEOANTIGEN_PIPELINE:WES_WORKFLOW:BWA_MEM":
    bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
    samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
END_VERSIONS
