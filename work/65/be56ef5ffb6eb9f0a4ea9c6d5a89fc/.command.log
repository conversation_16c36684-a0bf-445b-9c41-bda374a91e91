WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
Version Info: ### PLEASE UPGRADE SALMON ###
### A newer version of salmon with important bug fixes and improvements is available. ####
###
The newest version, available at https://github.com/COMBINE-lab/salmon/releases
contains new features, improvements, and bug fixes; please upgrade at your
earliest convenience.
###
Sign up for the salmon mailing list to hear about new versions, features and updates at:
https://oceangenomics.com/subscribe
### salmon (selective-alignment-based) v1.9.0
### [ program ] => salmon 
### [ command ] => quant 
### [ threads ] => { 2 }
### [ libType ] => { A }
### [ index ] => { salmon }
### [ mates1 ] => { SAMPLE_02_T_rna_1.fastq.gz }
### [ mates2 ] => { SAMPLE_02_T_rna_2.fastq.gz }
### [ output ] => { SAMPLE_02_T }
Logs will be written to SAMPLE_02_T/logs
[2025-07-10 05:42:25.338] [jointLog] [info] setting maxHashResizeThreads to 2
[2025-07-10 05:42:25.339] [jointLog] [info] Fragment incompatibility prior below threshold.  Incompatible fragments will be ignored.
[2025-07-10 05:42:25.339] [jointLog] [info] Usage of --validateMappings implies use of minScoreFraction. Since not explicitly specified, it is being set to 0.65
[2025-07-10 05:42:25.339] [jointLog] [info] Setting consensusSlack to selective-alignment default of 0.35.
[2025-07-10 05:42:25.339] [jointLog] [info] parsing read library format
[2025-07-10 05:42:25.340] [jointLog] [info] There is 1 library.
[2025-07-10 05:42:25.356] [jointLog] [info] Loading pufferfish index
-----------------------------------------
| Loading contig table | Time = 6.7512 ms
-----------------------------------------
size = 83
-----------------------------------------
| Loading contig offsets | Time = 1.1046 ms
-----------------------------------------
-----------------------------------------
| Loading reference lengths | Time = 109.25 us
-----------------------------------------
-----------------------------------------
| Loading mphf table | Time = 3.3641 ms
-----------------------------------------
[2025-07-10 05:42:25.364] [jointLog] [info] Loading dense pufferfish index.
size = 42218
Number of ones: 82
Number of ones per inventory item: 512
Inventory entries filled: 1
-----------------------------------------
| Loading contig boundaries | Time = 1.692 ms
-----------------------------------------
size = 42218
-----------------------------------------
| Loading sequence | Time = 2.1615 ms
-----------------------------------------
size = 39758
-----------------------------------------
| Loading positions | Time = 2.0121 ms
-----------------------------------------
size = 40001
-----------------------------------------
| Loading reference sequence | Time = 303.08 us
-----------------------------------------
-----------------------------------------
| Loading reference accumulative lengths | Time = 229.08 us
-----------------------------------------
[2025-07-10 05:42:25.391] [jointLog] [info] done
[2025-07-10 05:42:25.435] [jointLog] [info] Index contained 1 targets
[2025-07-10 05:42:25.435] [jointLog] [info] Number of decoys : 1
[2025-07-10 05:42:25.435] [jointLog] [info] First decoy index : 0 




[2025-07-10 05:42:25.533] [jointLog] [warning] salmon was only able to assign 0 fragments to transcripts in the index, but the minimum number of required assigned fragments (--minAssignedFrags) was 10. This could be indicative of a mismatch between the reference and sample, or a very bad sample.  You can change the --minAssignedFrags parameter to force salmon to quantify with fewer assigned fragments (must have at least 1).
