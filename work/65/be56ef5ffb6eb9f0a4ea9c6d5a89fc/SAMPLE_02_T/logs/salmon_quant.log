[2025-07-10 05:42:25.338] [jointLog] [info] setting maxHashResizeThreads to 2
[2025-07-10 05:42:25.339] [jointLog] [info] Fragment incompatibility prior below threshold.  Incompatible fragments will be ignored.
[2025-07-10 05:42:25.339] [jointLog] [info] Usage of --validateMappings implies use of minScoreFraction. Since not explicitly specified, it is being set to 0.65
[2025-07-10 05:42:25.339] [jointLog] [info] Setting consensusSlack to selective-alignment default of 0.35.
[2025-07-10 05:42:25.339] [jointLog] [info] parsing read library format
[2025-07-10 05:42:25.340] [jointLog] [info] There is 1 library.
[2025-07-10 05:42:25.356] [jointLog] [info] Loading pufferfish index
[2025-07-10 05:42:25.364] [jointLog] [info] Loading dense pufferfish index.
[2025-07-10 05:42:25.391] [jointLog] [info] done
[2025-07-10 05:42:25.435] [jointLog] [info] Index contained 1 targets
[2025-07-10 05:42:25.435] [jointLog] [info] Number of decoys : 1
[2025-07-10 05:42:25.435] [jointLog] [info] First decoy index : 0 
[2025-07-10 05:42:25.533] [jointLog] [warning] salmon was only able to assign 0 fragments to transcripts in the index, but the minimum number of required assigned fragments (--minAssignedFrags) was 10. This could be indicative of a mismatch between the reference and sample, or a very bad sample.  You can change the --minAssignedFrags parameter to force salmon to quantify with fewer assigned fragments (must have at least 1).
