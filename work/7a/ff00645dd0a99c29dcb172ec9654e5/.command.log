Unable to find image 'quay.io/biocontainers/mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40:66ed1b38d280722529bb8a0167b0cf02f8a0b488-0' locally
66ed1b38d280722529bb8a0167b0cf02f8a0b488-0: Pulling from biocontainers/mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40
190a414298e2: Pulling fs layer
190a414298e2: Download complete
190a414298e2: Pull complete
Digest: sha256:e7ac17308c079ab767d0ee43ac948346c7a6f96f6adf42a2872d2d070f1ea594
Status: Downloaded newer image for quay.io/biocontainers/mulled-v2-fe8faa35dbf6dc65a0f7f5d4ea12e31a79f73e40:66ed1b38d280722529bb8a0167b0cf02f8a0b488-0
WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 199896 sequences (20000171 bp)...
[M::process] read 199900 sequences (20000088 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (0, 8, 1, 0)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] skip orientation FR as there are not enough pairs
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 199896 reads in 5.189 CPU sec, 3.211 real sec
[M::process] read 133676 sequences (13374406 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (2, 14, 7, 3)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (25, 53, 1419)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 4207)
[M::mem_pestat] mean and std.dev: (599.92, 1089.74)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 5601)
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 199900 reads in 9.998 CPU sec, 6.031 real sec
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (1, 4, 1, 2)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] skip orientation FR as there are not enough pairs
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 133676 reads in 3.268 CPU sec, 1.980 real sec
[main] Version: 0.7.17-r1188
[main] CMD: bwa mem -t 2 -R @RG\tID:SAMPLE_01_T\tSM:SAMPLE_01_T\tPL:ILLUMINA ./genome.fasta SAMPLE_01_T_wes_1.fastq.gz SAMPLE_01_T_wes_2.fastq.gz
[main] Real time: 11.666 sec; CPU: 18.773 sec
[bam_sort_core] merging from 0 files and 2 in-memory blocks...
