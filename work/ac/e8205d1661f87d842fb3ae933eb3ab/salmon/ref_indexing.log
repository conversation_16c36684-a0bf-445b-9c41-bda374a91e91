[2025-07-10 06:18:13.229] [puff::index::jointLog] [info] Running fixFasta
[2025-07-10 06:18:13.243] [puff::index::jointLog] [info] Replaced 0 non-ATCG nucleotides
[2025-07-10 06:18:13.244] [puff::index::jointLog] [info] Clipped poly-A tails from 0 transcripts
[2025-07-10 06:18:13.247] [puff::index::jointLog] [info] Filter size not provided; estimating from number of distinct k-mers
[2025-07-10 06:18:13.249] [puff::index::jointLog] [info] ntHll estimated 64029 distinct k-mers, setting filter size to 2^21
[2025-07-10 06:18:13.282] [puff::index::jointLog] [info] Starting the Pufferfish indexing by reading the GFA binary file.
[2025-07-10 06:18:13.282] [puff::index::jointLog] [info] Setting the index/BinaryGfa directory salmon
[2025-07-10 06:18:13.284] [puff::index::jointLog] [info] Done wrapping the rank vector with a rank9sel structure.
[2025-07-10 06:18:13.284] [puff::index::jointLog] [info] contig count for validation: 82
[2025-07-10 06:18:13.285] [puff::index::jointLog] [info] Total # of Contigs : 82
[2025-07-10 06:18:13.285] [puff::index::jointLog] [info] Total # of numerical Contigs : 82
[2025-07-10 06:18:13.285] [puff::index::jointLog] [info] Total # of contig vec entries: 147
[2025-07-10 06:18:13.285] [puff::index::jointLog] [info] bits per offset entry 8
[2025-07-10 06:18:13.285] [puff::index::jointLog] [info] Done constructing the contig vector. 83
[2025-07-10 06:18:13.286] [puff::index::jointLog] [info] # segments = 82
[2025-07-10 06:18:13.286] [puff::index::jointLog] [info] total length = 42218
[2025-07-10 06:18:13.286] [puff::index::jointLog] [info] Reading the reference files ...
[2025-07-10 06:18:13.288] [puff::index::jointLog] [info] positional integer width = 16
[2025-07-10 06:18:13.288] [puff::index::jointLog] [info] seqSize = 42218
[2025-07-10 06:18:13.288] [puff::index::jointLog] [info] rankSize = 42218
[2025-07-10 06:18:13.288] [puff::index::jointLog] [info] edgeVecSize = 0
[2025-07-10 06:18:13.288] [puff::index::jointLog] [info] num keys = 39758
[2025-07-10 06:18:13.306] [puff::index::jointLog] [info] mphf size = 0.0255585 MB
[2025-07-10 06:18:13.306] [puff::index::jointLog] [info] chunk size = 21109
[2025-07-10 06:18:13.306] [puff::index::jointLog] [info] chunk 0 = [0, 21109)
[2025-07-10 06:18:13.306] [puff::index::jointLog] [info] chunk 1 = [21109, 42188)
[2025-07-10 06:18:13.308] [puff::index::jointLog] [info] finished populating pos vector
[2025-07-10 06:18:13.308] [puff::index::jointLog] [info] writing index components
[2025-07-10 06:18:13.310] [puff::index::jointLog] [info] finished writing dense pufferfish index
