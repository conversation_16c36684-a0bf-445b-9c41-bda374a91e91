WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
usage: OptiType [-h] --input FQ [FQ ...] (--rna | --dna) [--beta B]
                [--enumerate N] --outdir OUTDIR [--prefix PREFIX] [--verbose]
                [--config CONFIG]
OptiType: error: argument --config/-c: can't open 'eval export PYTHONNOUSERSITE="1"
export R_PROFILE_USER="/.Rprofile"
export R_ENVIRON_USER="/.Renviron"
export JULIA_DEPOT_PATH="/usr/local/share/julia"
export PATH="$PATH:/Users/<USER>/Downloads/immune_neoantigen_pipeline/bin"; /bin/bash -euo pipefail /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ae/97aff891721d1975fbe13855e1c819/.command.run nxf_trace': [Errno 2] No such file or directory: 'eval export PYTHONNOUSERSITE="1"\nexport R_PROFILE_USER="/.Rprofile"\nexport R_ENVIRON_USER="/.Renviron"\nexport JULIA_DEPOT_PATH="/usr/local/share/julia"\nexport PATH="$PATH:/Users/<USER>/Downloads/immune_neoantigen_pipeline/bin"; /bin/bash -euo pipefail /Users/<USER>/Downloads/immune_neoantigen_pipeline/work/ae/97aff891721d1975fbe13855e1c819/.command.run nxf_trace'
