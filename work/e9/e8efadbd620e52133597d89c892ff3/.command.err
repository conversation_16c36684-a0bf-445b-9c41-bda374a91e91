WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
Traceback (most recent call last):
  File "/usr/local/bin/OptiType/OptiTypePipeline.py", line 311, in <module>
    pos, read_details = ht.pysam_to_hdf(bam_paths[0])
  File "/usr/local/bin/OptiType/hlatyper.py", line 240, in pysam_to_hdf
    pos_df.columns = sam.references[:]
  File "/usr/local/lib/python2.7/dist-packages/pandas/core/generic.py", line 5080, in __setattr__
    return object.__setattr__(self, name, value)
  File "pandas/_libs/properties.pyx", line 69, in pandas._libs.properties.AxisProperty.__set__
  File "/usr/local/lib/python2.7/dist-packages/pandas/core/generic.py", line 638, in _set_axis
    self._data.set_axis(axis, labels)
  File "/usr/local/lib/python2.7/dist-packages/pandas/core/internals/managers.py", line 155, in set_axis
    'values have {new} elements'.format(old=old_len, new=new_len))
ValueError: Length mismatch: Expected axis has 0 elements, new values have 11179 elements
